<template>
  <div
    class="chart-container relative w-full h-full overflow-hidden"
    :style="{
      height: height || '100%',
      width: width || '100%'
    }"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="chart-overlay flex items-center justify-center">
      <div class="flex flex-col items-center gap-2">
        <div class="loading-spinner"></div>
        <span class="text-sm text-secondary">加载中...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="chart-overlay flex items-center justify-center">
      <div class="flex flex-col items-center gap-4">
        <div class="error-icon text-error w-12 h-12">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <span class="text-sm text-secondary text-center max-w-4/5">{{ error }}</span>
        <button 
          class="px-4 py-2 border border-error rounded text-sm text-error cursor-pointer transition hover:bg-error hover:text-white"
          @click="$emit('retry')"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div v-if="title" class="p-4">
      <h3 class="text-lg font-bold text-primary m-0">{{ title }}</h3>
    </div>
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) =>
      ['line', 'bar', 'pie', 'doughnut', 'radar'].includes(value)
  },
  data: {
    type: Object,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: ''
  },
  options: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['retry'])

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()

  // 监听容器大小变化，自动调整图表大小
  const resizeObserver = new ResizeObserver(() => {
    chartInstance.resize()
  })
  resizeObserver.observe(chartRef.value)

  // 组件卸载时清理observer
  onUnmounted(() => {
    resizeObserver.disconnect()
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    ...props.options,
    backgroundColor: 'transparent'
  }, true)
}

// 监听数据变化
watch(
  () => [props.data, props.options],
  () => {
    if (chartInstance) {
      updateChart()
    } else {
      initChart()
    }
  },
  { deep: true }
)

// 生命周期钩子
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.chart-container {
  background-color: var(--nav-bg-color);
  color: var(--text-color);
}

.chart-overlay {
  position: absolute;
  inset: 0;
  background-color: var(--bg-color);
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.chart-content {
  height: calc(100% - 4rem);
  width: 100%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>