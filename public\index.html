<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>海关领导视窗</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@3.5.0/dist/vue.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: #001529;
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        #app {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .tabs {
            display: flex;
            background-color: #002140;
            padding: 10px;
        }
        .tab {
            padding: 10px 20px;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        .tab:hover {
            background-color: #1890ff44;
        }
        .tab.active {
            background-color: #1890ff;
            color: white;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 15px;
            overflow: auto;
        }
        .section {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        .card {
            background-color: #002140;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: scale(1.05);
        }
        .chart {
            grid-column: span 3;
            height: 400px;
            background-color: #002140;
            border-radius: 8px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="tabs">
            <div 
                v-for="tab in tabs" 
                :key="tab.key"
                @click="currentTab = tab.key"
                :class="['tab', { active: currentTab === tab.key }]"
            >
                {{ tab.label }}
            </div>
        </div>
        
        <div class="content">
            <div v-if="currentTab === 'processing'" class="section">
                <div class="card">
                    <h3>总加工增值额</h3>
                    <p>{{ data.processing.overview.totalValue }}万元</p>
                </div>
                <div class="card">
                    <h3>联网企业数</h3>
                    <p>{{ data.processing.overview.enterpriseCount }}家</p>
                </div>
                <div class="card">
                    <h3>同比增长率</h3>
                    <p>{{ data.processing.overview.growthRate }}%</p>
                </div>

                <div id="region-chart" class="chart"></div>
                <div id="trend-chart" class="chart"></div>
                <div id="top-enterprises-chart" class="chart"></div>
            </div>
            
            <div v-else class="section">
                <div class="card">
                    <h3>零关税进口</h3>
                    <p>{{ data.transport.overview.zeroTariffImports }}台</p>
                </div>
                <div class="card">
                    <h3>车辆类型数</h3>
                    <p>{{ data.transport.overview.vehicleTypes }}种</p>
                </div>
                <div class="card">
                    <h3>总交通工具值</h3>
                    <p>{{ data.transport.overview.totalValue }}万元</p>
                </div>

                <div id="vehicle-chart" class="chart"></div>
                <div id="transport-trend-chart" class="chart"></div>
                <div id="tax-exemption-chart" class="chart"></div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue

        createApp({
            setup() {
                const currentTab = ref('processing')
                const tabs = [
                    { key: 'processing', label: '生产加工' },
                    { key: 'transport', label: '交通工具' }
                ]
                const data = ref({
                    processing: { 
                        overview: {}, 
                        regionDistribution: [], 
                        monthlyTrend: [],
                        topEnterprises: [],
                        productTop5: []
                    },
                    transport: { 
                        overview: {}, 
                        vehicleDistribution: [], 
                        monthlyTrend: [],
                        taxExemption: {}
                    }
                })

                const fetchData = async () => {
                    const response = await fetch('/api/data')
                    data.value = await response.json()
                    renderCharts()
                }

                const renderCharts = () => {
                    // 生产加工图表
                    const regionChart = echarts.init(document.getElementById('region-chart'))
                    regionChart.setOption({
                        title: { text: '区域加工增值分布', textStyle: { color: 'white' } },
                        series: [{
                            type: 'pie',
                            data: data.value.processing.regionDistribution,
                            label: { color: 'white' }
                        }]
                    })

                    const trendChart = echarts.init(document.getElementById('trend-chart'))
                    trendChart.setOption({
                        title: { text: '加工增值月度趋势', textStyle: { color: 'white' } },
                        xAxis: { 
                            type: 'category', 
                            data: data.value.processing.monthlyTrend.map(t => t.month),
                            axisLabel: { color: 'white' }
                        },
                        yAxis: { type: 'value', axisLabel: { color: 'white' } },
                        series: [{
                            type: 'line',
                            data: data.value.processing.monthlyTrend.map(t => t.value)
                        }]
                    })

                    const topEnterprisesChart = echarts.init(document.getElementById('top-enterprises-chart'))
                    topEnterprisesChart.setOption({
                        title: { text: '加工增值TOP企业', textStyle: { color: 'white' } },
                        xAxis: {
                            type: 'category',
                            data: data.value.processing.topEnterprises.map(e => e.name),
                            axisLabel: { color: 'white' }
                        },
                        yAxis: { 
                            type: 'value', 
                            axisLabel: { color: 'white' },
                            name: '加工增值额(亿元)'
                        },
                        series: [{
                            type: 'bar',
                            data: data.value.processing.topEnterprises.map(e => e.value)
                        }]
                    })

                    // 交通工具图表
                    const vehicleChart = echarts.init(document.getElementById('vehicle-chart'))
                    vehicleChart.setOption({
                        title: { text: '车辆类型分布', textStyle: { color: 'white' } },
                        series: [{
                            type: 'pie',
                            data: data.value.transport.vehicleDistribution,
                            label: { color: 'white' }
                        }]
                    })

                    const transportTrendChart = echarts.init(document.getElementById('transport-trend-chart'))
                    transportTrendChart.setOption({
                        title: { text: '交通工具月度趋势', textStyle: { color: 'white' } },
                        xAxis: { 
                            type: 'category', 
                            data: data.value.transport.monthlyTrend.map(t => t.month),
                            axisLabel: { color: 'white' }
                        },
                        yAxis: { type: 'value', axisLabel: { color: 'white' } },
                        series: [{
                            type: 'line',
                            data: data.value.transport.monthlyTrend.map(t => t.value)
                        }]
                    })

                    const taxExemptionChart = echarts.init(document.getElementById('tax-exemption-chart'))
                    taxExemptionChart.setOption({
                        title: { text: '交通工具减免税企业排行', textStyle: { color: 'white' } },
                        xAxis: {
                            type: 'category',
                            data: data.value.transport.taxExemption.enterpriseRanking.map(e => e.name),
                            axisLabel: { color: 'white' }
                        },
                        yAxis: { 
                            type: 'value', 
                            axisLabel: { color: 'white' },
                            name: '减免税额(万元)'
                        },
                        series: [{
                            type: 'bar',
                            data: data.value.transport.taxExemption.enterpriseRanking.map(e => e.value)
                        }]
                    })
                }

                onMounted(fetchData)

                return {
                    currentTab,
                    tabs,
                    data
                }
            }
        }).mount('#app')
    </script>
</body>
</html> 