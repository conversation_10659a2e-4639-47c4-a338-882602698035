<template>
  <div class="trend-analysis-stats">
    <!-- 数据卡片 -->
    <div class="data-cards">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="data-card">
              <div class="card-title">月均减免税额</div>
              <div class="card-value">{{ loading ? '-' : '￥9,350万' }}</div>
              <div class="card-trend up">
                <el-icon><CaretTop /></el-icon>
                <span>同比 +15.2%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="data-card">
              <div class="card-title">增长率</div>
              <div class="card-value">{{ loading ? '-' : '+18.5%' }}</div>
              <div class="card-trend up">
                <el-icon><CaretTop /></el-icon>
                <span>环比 +2.8%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="data-card">
              <div class="card-title">预计年度总额</div>
              <div class="card-value">{{ loading ? '-' : '￥11.2亿' }}</div>
              <div class="card-trend up">
                <el-icon><CaretTop /></el-icon>
                <span>完成率 85.2%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="16">
        <!-- 月度趋势分析 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">月度趋势分析</div>
            <div class="chart" ref="monthlyTrendChart"></div>
          </div>
        </el-col>
        <!-- 同比环比分析 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">同比环比分析</div>
            <div class="chart" ref="comparisonChart"></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 接收父组件传递的数据和加载状态
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// 图表实例
const monthlyTrendChart = ref(null)
const comparisonChart = ref(null)
let monthlyTrendInstance = null
let comparisonInstance = null

// 初始化月度趋势图表
const initMonthlyTrendChart = () => {
  if (!monthlyTrendChart.value) return

  monthlyTrendInstance = echarts.init(monthlyTrendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['减免税额', '同比增长'],
      textStyle: {
        color: 'var(--text-color)'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisPointer: {
          type: 'shadow'
        },
        axisLine: {
          lineStyle: {
            color: 'var(--text-color-secondary)'
          }
        },
        axisLabel: {
          color: 'var(--text-color)'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '减免税额',
        min: 0,
        max: 12000,
        interval: 2000,
        axisLabel: {
          formatter: '{value}万',
          color: 'var(--text-color)'
        },
        axisLine: {
          lineStyle: {
            color: 'var(--text-color-secondary)'
          }
        }
      },
      {
        type: 'value',
        name: '同比增长',
        min: 0,
        max: 25,
        interval: 5,
        axisLabel: {
          formatter: '{value}%',
          color: 'var(--text-color)'
        },
        axisLine: {
          lineStyle: {
            color: 'var(--text-color-secondary)'
          }
        }
      }
    ],
    series: [
      {
        name: '减免税额',
        type: 'bar',
        data: [8200, 7500, 9100, 9350, 10200, 11500],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      },
      {
        name: '同比增长',
        type: 'line',
        yAxisIndex: 1,
        data: [12.5, 13.8, 15.2, 16.5, 18.2, 20.5],
        itemStyle: {
          color: '#ff9f7f'
        }
      }
    ]
  }
  monthlyTrendInstance.setOption(option)
}

// 初始化同比环比图表
const initComparisonChart = () => {
  if (!comparisonChart.value) return

  comparisonInstance = echarts.init(comparisonChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['同比', '环比'],
      textStyle: {
        color: 'var(--text-color)'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          lineStyle: {
            color: 'var(--text-color-secondary)'
          }
        },
        axisLabel: {
          color: 'var(--text-color)'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '增长率',
        axisLabel: {
          formatter: '{value}%',
          color: 'var(--text-color)'
        },
        axisLine: {
          lineStyle: {
            color: 'var(--text-color-secondary)'
          }
        }
      }
    ],
    series: [
      {
        name: '同比',
        type: 'bar',
        data: [12.5, 13.8, 15.2, 16.5, 18.2, 20.5],
        itemStyle: {
          color: '#91cc75'
        }
      },
      {
        name: '环比',
        type: 'bar',
        data: [5.2, 6.8, 8.5, 7.2, 8.8, 9.5],
        itemStyle: {
          color: '#fac858'
        }
      }
    ]
  }
  comparisonInstance.setOption(option)
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (!newData) return
    // 更新图表数据
    if (monthlyTrendInstance && comparisonInstance) {
      // 这里可以根据实际数据更新图表
    }
  }
)

// 组件挂载时初始化图表
onMounted(() => {
  initMonthlyTrendChart()
  initComparisonChart()

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    monthlyTrendInstance?.resize()
    comparisonInstance?.resize()
  })
})

// 组件卸载时销毁图表实例
onUnmounted(() => {
  monthlyTrendInstance?.dispose()
  comparisonInstance?.dispose()
  window.removeEventListener('resize', () => {
    monthlyTrendInstance?.resize()
    comparisonInstance?.resize()
  })
})
</script>

<style scoped>
.trend-analysis-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.data-cards {
  margin-bottom: 20px;
}

.data-card {
  text-align: center;
  padding: 10px;
}

.card-title {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8px;
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.card-trend.up {
  color: #67c23a;
}

.card-trend.down {
  color: #f56c6c;
}

.charts-container {
  flex: 1;
  min-height: 0;
}

.chart-wrapper {
  height: 100%;
  background-color: var(--component-bg);
  border-radius: 4px;
  padding: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;
}

.chart {
  height: calc(100% - 32px);
}
</style>