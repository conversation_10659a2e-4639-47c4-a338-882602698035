<template>
  <div class="dashboard-view">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="error" class="error-container">
      <el-alert
        title="加载失败"
        type="error"
        :description="error"
        show-icon
      />
    </div>
    <div v-else>
      <!-- 顶部统计卡片 -->
      <StatisticsCards 
        :processing-data="processingData" 
        :transport-data="transportData" 
        :tax-exemption-data="taxExemptionData" 
      />

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="charts-row" :class="{'charts-row-mobile': deviceStore.isMobile}">
          <ErpEnterpriseChart :data="erpData" />
          <TransportAnalysisChart :data="transportData" />
        </div>

        <div class="charts-row" :class="{'charts-row-mobile': deviceStore.isMobile}">
          <TaxExemptionTrendChart :data="taxExemptionData" />
          <ErpRegionChart :data="erpData" />
        </div>

        <div class="charts-row" :class="{'charts-row-mobile': deviceStore.isMobile}">
          <ZeroTaxVehicleChart :data="zeroTaxData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useProcessingStore } from '../../stores/processing.js';
import { useTransportStore } from '../../stores/transport.js';
import { useTaxExemptionStore } from '../../stores/taxExemption.js';
import { useDeviceStore } from '../../stores/device.js';

// 组件导入
import StatisticsCards from './components/StatisticsCards.vue';
import ErpEnterpriseChart from './components/ErpEnterpriseChart.vue';
import TransportAnalysisChart from './components/TransportAnalysisChart.vue';
import TaxExemptionTrendChart from './components/TaxExemptionTrendChart.vue';
import ErpRegionChart from './components/ErpRegionChart.vue';
import ZeroTaxVehicleChart from './components/ZeroTaxVehicleChart.vue';

// Store实例
const processingStore = useProcessingStore();
const transportStore = useTransportStore();
const taxExemptionStore = useTaxExemptionStore();
const deviceStore = useDeviceStore();

// 数据状态
const loading = ref(true);
const error = ref(null);

// 计算属性：获取各模块数据
const processingData = computed(() => processingStore.stats);
const erpData = computed(() => processingStore.erpStats);
const transportData = computed(() => transportStore.stats);
const zeroTaxData = computed(() => transportStore.zeroTaxStats);
const taxExemptionData = computed(() => taxExemptionStore.stats);

// 加载所有数据
const loadAllData = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    // 并行请求所有数据
    await Promise.all([
      processingStore.fetchStats(),
      transportStore.fetchStats(),
      taxExemptionStore.fetchStats()
    ]);
  } catch (err) {
    error.value = err.message || '加载数据失败';
    console.error('加载数据失败:', err);
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadAllData();
});
</script>

<style scoped>
.dashboard-view {
  width: 100%;
  padding: var(--app-content-padding);
}

.loading-container,
.error-container {
  width: 100%;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--app-component-bg);
  box-shadow: var(--app-card-shadow);
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.charts-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.charts-row > * {
  flex: 1;
  min-height: 300px;
}

.charts-row-mobile {
  flex-direction: column;
}

/* 移动设备适配 */
@media screen and (max-width: 768px) {
  .charts-row {
    flex-direction: column;
  }
}

/* 大屏设备适配 */
@media screen and (min-width: 1920px) {
  .charts-container {
    gap: 30px;
  }
  
  .charts-row > * {
    min-height: 400px;
  }
}
</style>