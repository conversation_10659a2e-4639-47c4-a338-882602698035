// 模拟数据服务层 - 海南海关领导视窗系统
// 按照需求表119行详细需求实现
class MockDataService {
  constructor() {
    this.delay = 300 // 模拟网络延迟
  }

  // 模拟异步请求
  async mockRequest(data) {
    return new Promise((resolve) => {
      setTimeout(() => resolve(data), this.delay)
    })
  }

  // 获取加工增值完整数据 - 按需求表752-813行实现
  async getProcessingData() {
    const data = {
      // 752-756: 货物总值及货量统计
      cargoValueAndVolume: {
        totalValue: 1256800, // 万元
        totalVolume: 894560, // 吨
        // 业务指标统计查询
        businessIndicators: {
          totalDeclarations: 2856,
          averageValue: 440.2,
          growthRate: 15.6
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['电子产品', '化工原料', '机械设备', '纺织品', '食品'],
          series: [{
            name: '货物量(吨)',
            data: [156800, 234500, 189600, 145200, 168400]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [
            {
              name: '货物总值(万元)',
              data: [98500, 102300, 118600, 125400, 134200, 142800, 156900, 168200, 175600, 182400, 195800, 208900]
            },
            {
              name: '货物总量(吨)',
              data: [68400, 71200, 78900, 82600, 87300, 91800, 96500, 102100, 107800, 113200, 118600, 124300]
            }
          ]
        }
      },

      // 757-760: 免征税款统计
      taxExemption: {
        totalAmount: 325800, // 万元
        // 业务指标统计查询
        businessIndicators: {
          exemptionCount: 1856,
          averageExemption: 175.4,
          exemptionRate: 23.8
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['原材料进口', '设备进口', '零部件进口', '半成品进口', '其他'],
          series: [{
            name: '免征税款(万元)',
            data: [128600, 89400, 56700, 34200, 16900]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '免征税款(万元)',
            data: [21400, 23600, 26800, 28900, 31200, 33500, 35800, 38100, 40600, 43200, 45900, 48700]
          }]
        },
        // 地区分布
        regionDistribution: {
          categories: ['海口海关', '洋浦海关', '三亚海关', '海口港区', '马村海关', '八所海关'],
          series: [{
            name: '免征税款(万元)',
            data: [128600, 86400, 54200, 32800, 15600, 8200]
          }]
        }
      },

      // 761-764: 备案企业统计
      registeredEnterprises: {
        total: 1568,
        // 业务指标统计查询
        businessIndicators: {
          newRegistrations: 156,
          activeEnterprises: 1456,
          suspendedEnterprises: 112
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['生产型企业', '贸易型企业', '物流型企业', '服务型企业', '混合型企业'],
          series: [{
            name: '企业数量',
            data: [568, 445, 328, 156, 71]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '新增备案企业',
            data: [8, 12, 15, 18, 22, 25, 28, 32, 35, 38, 42, 45]
          }]
        }
      },

      // 765-772: 进港进口原辅料减免税费企业排行
      importMaterialTaxReduction: {
        // 企业排行信息
        enterpriseRanking: {
          categories: ['海南炼化有限公司', '中海油海南化学股份有限公司', '海马汽车股份有限公司', '椰树集团有限公司', '海航实业集团有限公司'],
          series: [{
            name: '减免税费(万元)',
            data: [25680, 18900, 15600, 12800, 9600]
          }]
        },
        // 统计信息
        statistics: {
          totalReduction: 82600,
          enterpriseCount: 156,
          averageReduction: 529.5
        }
      },

      // 773-776: 加工产值申报单数量
      processingValueDeclarations: {
        total: 2856,
        // 业务指标统计查询
        businessIndicators: {
          approvedDeclarations: 2698,
          pendingDeclarations: 158,
          rejectedDeclarations: 0
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['电子信息', '生物医药', '新材料', '装备制造', '石化', '其他'],
          series: [{
            name: '申报单数量',
            data: [856, 645, 498, 356, 289, 212]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '申报单数量',
            data: [186, 198, 215, 228, 245, 262, 278, 295, 312, 328, 345, 364]
          }]
        }
      },

      // 777-780: 发货申请
      shipmentApplications: {
        total: 4568,
        // 业务指标统计查询
        businessIndicators: {
          approvedApplications: 4356,
          pendingApplications: 212,
          rejectedApplications: 0
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['内销发货', '出口发货', '转厂发货', '退运发货', '其他'],
          series: [{
            name: '发货申请数量',
            data: [2156, 1568, 645, 156, 43]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '发货申请数量',
            data: [298, 315, 342, 368, 395, 422, 448, 475, 502, 528, 555, 582]
          }]
        }
      },

      // 免征税款统计
      taxExemption: {
        totalAmount: 32580, // 万元
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '免征税款(万元)',
              data: [4200, 4800, 5200, 5600, 6180, 6600]
            }
          ]
        },
        byRegion: {
          categories: ['海口海关', '洋浦海关', '三亚海关', '海口港区', '马村海关'],
          series: [
            {
              name: '免征税款(万元)',
              data: [12800, 8600, 5400, 3200, 2580]
            }
          ]
        }
      },

      // 备案企业统计
      registeredCompanies: {
        total: 156,
        byType: {
          data: [
            { name: '生产型企业', value: 68 },
            { name: '贸易型企业', value: 45 },
            { name: '物流型企业', value: 28 },
            { name: '其他类型', value: 15 }
          ]
        },
        monthlyGrowth: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '新增备案企业',
              data: [8, 12, 15, 18, 22, 25]
            }
          ]
        }
      },

      // ERP联网企业数量
      erpCompanies: {
        inZone: 89, // 区内企业
        outZone: 67, // 区外企业
        total: 156,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '区内企业',
              data: [72, 75, 78, 82, 86, 89]
            },
            {
              name: '区外企业',
              data: [58, 60, 62, 64, 66, 67]
            }
          ]
        }
      },

      // 781-788: 加工增值企业统计 - 重点企业类型分布
      keyEnterpriseTypes: {
        // 业务指标统计查询
        businessIndicators: {
          totalKeyEnterprises: 456,
          highTechEnterprises: 289,
          traditionalEnterprises: 167
        },
        // 业务量维度统计-图可视化展示 + 排名统计可视化展示
        distribution: {
          categories: ['电子信息', '生物医药', '新材料', '装备制造', '石化工业', '食品加工', '纺织服装', '其他'],
          series: [{
            name: '企业数量',
            data: [128, 89, 67, 54, 43, 32, 28, 15]
          }]
        }
      },

      // 785-788: 主要地区市县分布
      regionalDistribution: {
        // 业务指标统计查询
        businessIndicators: {
          totalRegions: 18,
          activeRegions: 16,
          leadingRegions: 5
        },
        // 业务量维度统计-图可视化展示 + 排名统计可视化展示
        distribution: {
          categories: ['海口市', '三亚市', '洋浦经济开发区', '澄迈县', '文昌市', '琼海市', '儋州市', '万宁市', '东方市', '其他'],
          series: [{
            name: '企业数量',
            data: [456, 289, 234, 156, 128, 89, 67, 45, 32, 72]
          }]
        }
      },

      // 789-792: 加工增值产值TOP5企业
      top5ProcessingEnterprises: {
        // 业务指标统计查询
        businessIndicators: {
          totalOutput: 1256.8, // 亿元
          averageOutput: 251.36,
          growthRate: 18.5
        },
        // 业务量维度统计-图可视化展示 + 排名统计可视化展示
        ranking: {
          categories: ['海南炼化有限公司', '中海油海南化学股份有限公司', '海马汽车股份有限公司', '椰树集团有限公司', '海航实业集团有限公司'],
          series: [{
            name: '产值(亿元)',
            data: [356.8, 289.6, 234.5, 189.2, 186.7]
          }]
        }
      },

      // 793-798: ERP联网企业数量
      erpConnectedEnterprises: {
        // 区内企业数量
        inZoneEnterprises: {
          total: 892,
          businessIndicators: {
            connectedCount: 856,
            connectionRate: 95.9,
            newConnections: 45
          },
          volumeStats: {
            categories: ['生产企业', '贸易企业', '物流企业', '服务企业'],
            series: [{
              name: '区内企业数量',
              data: [456, 234, 156, 46]
            }]
          }
        },
        // 区外企业数量
        outZoneEnterprises: {
          total: 678,
          businessIndicators: {
            connectedCount: 634,
            connectionRate: 93.5,
            newConnections: 32
          },
          volumeStats: {
            categories: ['生产企业', '贸易企业', '物流企业', '服务企业'],
            series: [{
              name: '区外企业数量',
              data: [345, 189, 112, 32]
            }]
          }
        }
      },

      // 799-803: 生产加工分析统计 - 主要加工产品TOP5
      mainProcessingProducts: {
        // 业务指标统计查询
        businessIndicators: {
          totalProducts: 1568,
          mainCategories: 12,
          outputValue: 2568.9 // 万吨
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['石化产品', '电子元器件', '汽车零部件', '医药制品', '食品饮料', '纺织品', '机械设备', '其他'],
          series: [{
            name: '产量(万吨)',
            data: [568.9, 234.6, 189.3, 156.8, 145.2, 128.7, 98.4, 77.0]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '总产量(万吨)',
            data: [156.8, 168.9, 189.2, 198.6, 212.4, 225.8, 238.9, 252.3, 265.7, 278.4, 291.6, 305.2]
          }]
        },
        // 排名统计可视化展示
        ranking: {
          categories: ['石化产品', '电子元器件', '汽车零部件', '医药制品', '食品饮料'],
          series: [{
            name: '产量(万吨)',
            data: [568.9, 234.6, 189.3, 156.8, 145.2]
          }]
        }
      },

      // 804-808: 热门料件与成品对应关系
      materialProductRelation: {
        // 业务指标统计查询
        businessIndicators: {
          totalRelations: 2856,
          activeRelations: 2634,
          newRelations: 156
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['原油→石化产品', '硅片→电子元器件', '钢材→汽车零部件', '原料药→医药制品', '原料→食品饮料'],
          series: [{
            name: '对应关系数量',
            data: [856, 634, 456, 289, 234]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '新增对应关系',
            data: [8, 12, 15, 18, 22, 25, 28, 32, 35, 38, 42, 45]
          }]
        },
        // 排名统计可视化展示
        ranking: {
          categories: ['原油→石化产品', '硅片→电子元器件', '钢材→汽车零部件', '原料药→医药制品', '原料→食品饮料'],
          series: [{
            name: '使用频次',
            data: [2856, 2134, 1689, 1234, 987]
          }]
        }
      },

      // 809-813: 内销统计 - 主要区域TOP5排名
      domesticSalesRegions: {
        // 业务指标统计查询
        businessIndicators: {
          totalSales: 15680, // 万元
          regionCount: 31,
          averageSales: 506.1
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['华南地区', '华东地区', '华北地区', '西南地区', '华中地区', '东北地区', '西北地区', '其他'],
          series: [{
            name: '销售额(万元)',
            data: [4568, 3234, 2856, 2134, 1689, 1234, 987, 978]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '内销额(万元)',
            data: [1156, 1234, 1345, 1456, 1568, 1689, 1798, 1912, 2034, 2156, 2278, 2398]
          }]
        },
        // 排名统计可视化展示
        ranking: {
          categories: ['华南地区', '华东地区', '华北地区', '西南地区', '华中地区'],
          series: [{
            name: '销售额(万元)',
            data: [4568, 3234, 2856, 2134, 1689]
          }]
        }
      },

      // 814-817: 免征税额月度趋势统计
      monthlyTaxExemptionTrend: {
        // 业务指标统计查询
        businessIndicators: {
          totalExemption: 32580, // 万元
          monthlyAverage: 2715,
          growthRate: 15.6
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['原材料', '设备', '零部件', '半成品', '其他'],
          series: [{
            name: '免征税额(万元)',
            data: [12856, 8934, 5678, 3456, 1656]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '免征税额(万元)',
            data: [2156, 2234, 2345, 2456, 2568, 2689, 2798, 2912, 3034, 3156, 3278, 3398]
          }]
        }
      },

      // 加工增值企业统计
      processingCompanies: {
        // 重点企业类型分布
        keyCompanyTypes: {
          data: [
            { name: '电子信息', value: 35 },
            { name: '生物医药', value: 28 },
            { name: '新材料', value: 22 },
            { name: '装备制造', value: 18 },
            { name: '其他', value: 12 }
          ]
        },
        // 主要地区市县分布
        regionDistribution: {
          categories: ['海口市', '三亚市', '洋浦区', '澄迈县', '文昌市'],
          series: [
            {
              name: '企业数量',
              data: [45, 28, 22, 18, 12]
            }
          ]
        },
        // 加工增值产值TOP5企业
        top5Companies: {
          categories: ['海南炼化', '海马汽车', '椰树集团', '海航实业', '中海油海南'],
          series: [
            {
              name: '产值(亿元)',
              data: [25.6, 18.9, 15.2, 12.8, 9.6]
            }
          ]
        }
      }
    }

    return this.mockRequest(data)
  }

  // 获取交通运输工具完整数据 - 按需求表869-918行实现
  async getTransportData() {
    const data = {
      // 869-874: 零关税进口小汽车
      zeroDutyVehicles: {
        total: 13650,
        // 业务指标统计查询
        businessIndicators: {
          totalImports: 13650,
          averageValue: 28.5, // 万元
          totalValue: 38902.5, // 万元
          growthRate: 22.8
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['轿车', 'SUV', '新能源车', '豪华车', '商务车', '其他'],
          series: [{
            name: '进口数量(辆)',
            data: [6120, 4095, 2730, 546, 159, 0]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '零关税进口车辆(辆)',
            data: [856, 934, 1089, 1156, 1234, 1345, 1456, 1568, 1689, 1798, 1912, 2034]
          }]
        },
        // 排名统计可视化展示
        ranking: {
          categories: ['轿车', 'SUV', '新能源车', '豪华车', '商务车'],
          series: [{
            name: '进口数量(辆)',
            data: [6120, 4095, 2730, 546, 159]
          }]
        },
        // 业务指标统计计算可视化展示
        calculationStats: {
          categories: ['免税金额', '节省费用', '优惠比例'],
          values: [8934.6, 12567.8, 23.5] // 万元, 万元, %
        }
      },

      // 875-880: 封关后在自贸港购买的免税交通运输工具
      dutyFreeVehiclesAfterClosure: {
        total: 8920,
        // 业务指标统计查询
        businessIndicators: {
          totalPurchases: 8920,
          averageValue: 35.6, // 万元
          totalValue: 31755.2, // 万元
          growthRate: 28.9
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['小汽车', '游艇', '摩托车', '电动车', '其他交通工具'],
          series: [{
            name: '购买数量',
            data: [5340, 1784, 1250, 446, 100]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '免税购买数量',
            data: [634, 689, 745, 798, 856, 912, 967, 1023, 1078, 1134, 1189, 1245]
          }]
        },
        // 排名统计可视化展示
        ranking: {
          categories: ['小汽车', '游艇', '摩托车', '电动车', '其他交通工具'],
          series: [{
            name: '购买数量',
            data: [5340, 1784, 1250, 446, 100]
          }]
        },
        // 业务指标统计计算可视化展示
        calculationStats: {
          categories: ['免税金额', '节省费用', '优惠比例'],
          values: [12567.8, 18934.5, 37.2] // 万元, 万元, %
        }
      },

      // 881-886: 内地运输工具在岛内更换或使用进口零配件维修
      mainlandVehicleRepair: {
        total: 4560,
        // 业务指标统计查询
        businessIndicators: {
          totalRepairs: 4560,
          averageCost: 2.8, // 万元
          totalCost: 12768, // 万元
          repairRate: 89.5
        },
        // 业务量维度统计-图可视化展示
        volumeStats: {
          categories: ['发动机维修', '变速箱维修', '电子系统维修', '车身维修', '其他维修'],
          series: [{
            name: '维修数量',
            data: [1368, 1094, 912, 730, 456]
          }]
        },
        // 时间维度统计趋势-图可视化展示
        timeTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          series: [{
            name: '维修数量',
            data: [298, 324, 356, 389, 412, 445, 478, 501, 534, 567, 590, 623]
          }]
        },
        // 排名统计可视化展示
        ranking: {
          categories: ['发动机维修', '变速箱维修', '电子系统维修', '车身维修', '其他维修'],
          series: [{
            name: '维修数量',
            data: [1368, 1094, 912, 730, 456]
          }]
        },
        // 业务指标统计计算可视化展示
        calculationStats: {
          categories: ['维修费用', '零配件费用', '人工费用'],
          values: [7680, 3456, 1632] // 万元
        }
      },

      // 887-894: 零关税业务综合分析
      zeroDutyBusinessAnalysis: {
        // 文件管理多维统计
        fileManagement: {
          totalFiles: 15680,
          processedFiles: 14892,
          pendingFiles: 788
        },
        // 综合分析数据
        comprehensiveAnalysis: {
          categories: ['政策效果', '经济影响', '企业受益', '市场反应'],
          series: [{
            name: '评分',
            data: [92.5, 89.3, 94.7, 87.8]
          }]
        }
      },

      // 895-902: 自用生产设备负面清单综合分析
      productionEquipmentAnalysis: {
        // 文件管理多维统计
        fileManagement: {
          totalFiles: 8920,
          processedFiles: 8456,
          pendingFiles: 464
        },
        // 综合分析数据
        comprehensiveAnalysis: {
          categories: ['清单完整性', '执行效果', '企业合规', '监管效率'],
          series: [{
            name: '评分',
            data: [95.2, 91.8, 88.6, 93.4]
          }]
        }
      },

      // 903-910: 交通工具及游艇减免税费企业排行
      vehicleYachtTaxReductionRanking: {
        // 文件管理统计
        fileManagement: {
          totalEnterprises: 456,
          rankedEnterprises: 432,
          totalReduction: 156780 // 万元
        },
        // 企业排行
        enterpriseRanking: {
          categories: ['海马汽车股份有限公司', '一汽海马汽车有限公司', '海南马自达汽车有限公司', '海口港务集团有限公司', '洋浦港务集团有限公司'],
          series: [{
            name: '减免税费(万元)',
            data: [28560, 21200, 16500, 12800, 9200]
          }]
        }
      },

      // 911-918: 自用生产设备减免税费企业排行
      productionEquipmentTaxReductionRanking: {
        // 文件管理统计
        fileManagement: {
          totalEnterprises: 789,
          rankedEnterprises: 756,
          totalReduction: 234560 // 万元
        },
        // 企业排行
        enterpriseRanking: {
          categories: ['海南炼化有限公司', '中海油海南化学股份有限公司', '海航技术股份有限公司', '椰树集团有限公司', '海马汽车股份有限公司'],
          series: [{
            name: '减免税费(万元)',
            data: [56000, 42000, 38000, 29000, 24000]
          }]
        }
      },

      // 封关后在自贸港购买的免税交通运输工具
      dutyFreeVehicles: {
        total: 892,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '免税车辆',
              data: [120, 135, 145, 155, 168, 169]
            }
          ]
        },
        byCategory: {
          data: [
            { name: '小汽车', value: 534 },
            { name: '游艇', value: 178 },
            { name: '摩托车', value: 125 },
            { name: '其他', value: 55 }
          ]
        }
      },

      // 内地运输工具维修统计
      mainlandVehicleRepair: {
        total: 456,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '维修车辆',
              data: [65, 72, 78, 82, 85, 74]
            }
          ]
        }
      },

      // 交通工具及游艇减免税费企业排行
      taxReductionRanking: {
        categories: ['海马汽车', '一汽海马', '海南马自达', '海口港务', '洋浦港务'],
        series: [
          {
            name: '减免税费(万元)',
            data: [2850, 2120, 1650, 1280, 920]
          }
        ]
      },

      // 自用生产设备减免税费企业排行
      equipmentTaxReduction: {
        categories: ['海南炼化', '中海油海南', '海航技术', '椰树集团', '海马汽车'],
        series: [
          {
            name: '减免税费(万元)',
            data: [5600, 4200, 3800, 2900, 2400]
          }
        ]
      }
    }

    return this.mockRequest(data)
  }

  // 获取实时统计数据
  async getRealTimeStats() {
    const data = {
      processing: {
        erpCompanies: Math.floor(Math.random() * 100) + 50,
        totalValue: Math.floor(Math.random() * 10000) + 10000,
        taxReduction: Math.floor(Math.random() * 5000) + 2000
      },
      transport: {
        vehicles: Math.floor(Math.random() * 500) + 1000,
        taxReduction: Math.floor(Math.random() * 2000) + 3000
      }
    }

    return this.mockRequest(data)
  }
}

export const mockDataService = new MockDataService()
