// 模拟数据服务层
class MockDataService {
  constructor() {
    this.delay = 500 // 模拟网络延迟
  }

  // 模拟异步请求
  async mockRequest(data) {
    return new Promise((resolve) => {
      setTimeout(() => resolve(data), this.delay)
    })
  }

  // 获取加工增值数据
  async getProcessingData() {
    const data = {
      erpCompanies: {
        categories: ['一月', '二月', '三月', '四月', '五月', '六月'],
        series: [
          {
            name: 'ERP联网企业',
            data: [8, 10, 12, 15, 18, 22]
          }
        ]
      },
      topRegions: {
        categories: ['海口海关', '洋浦海关', '海口港区', '三亚海关', '马村海关'],
        series: [
          {
            name: '企业数量',
            data: [18, 15, 12, 8, 6]
          }
        ]
      },
      topCompanies: {
        categories: ['海南炼化', '海马汽车', '椰树集团', '海航集团', '中海油'],
        series: [
          {
            name: '产值(万元)',
            data: [2500, 2200, 1800, 1500, 1200]
          }
        ]
      },
      // 统计数据
      stats: {
        totalCompanies: 89,
        totalValue: 12500,
        taxReduction: 3200,
        filedCompanies: 156
      }
    }

    return this.mockRequest(data)
  }

  // 获取交通运输工具数据
  async getTransportData() {
    const data = {
      vehicles: {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [
          {
            name: '零关税进口车辆',
            data: [120, 150, 180, 220, 280, 350]
          }
        ]
      },
      vehicleTypes: {
        data: [
          { name: '轿车', value: 45 },
          { name: 'SUV', value: 30 },
          { name: '货车', value: 15 },
          { name: '客车', value: 10 }
        ]
      },
      taxReduction: {
        categories: ['海马汽车', '一汽海马', '海南马自达', '海口港务', '洋浦港务'],
        series: [
          {
            name: '减免税费(万元)',
            data: [850, 720, 650, 580, 420]
          }
        ]
      },
      // 统计数据
      stats: {
        totalVehicles: 1365,
        totalTaxReduction: 3220,
        avgTaxReduction: 2.36
      }
    }

    return this.mockRequest(data)
  }

  // 获取实时统计数据
  async getRealTimeStats() {
    const data = {
      processing: {
        erpCompanies: Math.floor(Math.random() * 100) + 50,
        totalValue: Math.floor(Math.random() * 10000) + 10000,
        taxReduction: Math.floor(Math.random() * 5000) + 2000
      },
      transport: {
        vehicles: Math.floor(Math.random() * 500) + 1000,
        taxReduction: Math.floor(Math.random() * 2000) + 3000
      }
    }

    return this.mockRequest(data)
  }
}

export const mockDataService = new MockDataService()
