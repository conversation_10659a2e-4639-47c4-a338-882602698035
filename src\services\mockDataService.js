// 模拟数据服务层 - 海南海关领导视窗系统
class MockDataService {
  constructor() {
    this.delay = 300 // 模拟网络延迟
  }

  // 模拟异步请求
  async mockRequest(data) {
    return new Promise((resolve) => {
      setTimeout(() => resolve(data), this.delay)
    })
  }

  // 获取加工增值数据
  async getProcessingData() {
    const data = {
      // 货物总值及货量统计
      cargoStats: {
        totalValue: 125680, // 万元
        totalVolume: 89456, // 吨
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '货物总值(万元)',
              data: [18500, 19200, 21800, 23400, 25600, 27180]
            },
            {
              name: '货物总量(吨)',
              data: [12800, 13500, 15200, 16800, 18200, 19456]
            }
          ]
        }
      },

      // 免征税款统计
      taxExemption: {
        totalAmount: 32580, // 万元
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '免征税款(万元)',
              data: [4200, 4800, 5200, 5600, 6180, 6600]
            }
          ]
        },
        byRegion: {
          categories: ['海口海关', '洋浦海关', '三亚海关', '海口港区', '马村海关'],
          series: [
            {
              name: '免征税款(万元)',
              data: [12800, 8600, 5400, 3200, 2580]
            }
          ]
        }
      },

      // 备案企业统计
      registeredCompanies: {
        total: 156,
        byType: {
          data: [
            { name: '生产型企业', value: 68 },
            { name: '贸易型企业', value: 45 },
            { name: '物流型企业', value: 28 },
            { name: '其他类型', value: 15 }
          ]
        },
        monthlyGrowth: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '新增备案企业',
              data: [8, 12, 15, 18, 22, 25]
            }
          ]
        }
      },

      // ERP联网企业数量
      erpCompanies: {
        inZone: 89, // 区内企业
        outZone: 67, // 区外企业
        total: 156,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '区内企业',
              data: [72, 75, 78, 82, 86, 89]
            },
            {
              name: '区外企业',
              data: [58, 60, 62, 64, 66, 67]
            }
          ]
        }
      },

      // 加工增值企业统计
      processingCompanies: {
        // 重点企业类型分布
        keyCompanyTypes: {
          data: [
            { name: '电子信息', value: 35 },
            { name: '生物医药', value: 28 },
            { name: '新材料', value: 22 },
            { name: '装备制造', value: 18 },
            { name: '其他', value: 12 }
          ]
        },
        // 主要地区市县分布
        regionDistribution: {
          categories: ['海口市', '三亚市', '洋浦区', '澄迈县', '文昌市'],
          series: [
            {
              name: '企业数量',
              data: [45, 28, 22, 18, 12]
            }
          ]
        },
        // 加工增值产值TOP5企业
        top5Companies: {
          categories: ['海南炼化', '海马汽车', '椰树集团', '海航实业', '中海油海南'],
          series: [
            {
              name: '产值(亿元)',
              data: [25.6, 18.9, 15.2, 12.8, 9.6]
            }
          ]
        }
      }
    }

    return this.mockRequest(data)
  }

  // 获取交通运输工具数据
  async getTransportData() {
    const data = {
      // 零关税进口小汽车
      zeroDutyVehicles: {
        total: 1365,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '零关税进口车辆',
              data: [180, 195, 210, 235, 265, 280]
            }
          ]
        },
        byType: {
          data: [
            { name: '轿车', value: 612 },
            { name: 'SUV', value: 409 },
            { name: '新能源车', value: 273 },
            { name: '其他', value: 71 }
          ]
        }
      },

      // 封关后在自贸港购买的免税交通运输工具
      dutyFreeVehicles: {
        total: 892,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '免税车辆',
              data: [120, 135, 145, 155, 168, 169]
            }
          ]
        },
        byCategory: {
          data: [
            { name: '小汽车', value: 534 },
            { name: '游艇', value: 178 },
            { name: '摩托车', value: 125 },
            { name: '其他', value: 55 }
          ]
        }
      },

      // 内地运输工具维修统计
      mainlandVehicleRepair: {
        total: 456,
        monthlyTrend: {
          categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
          series: [
            {
              name: '维修车辆',
              data: [65, 72, 78, 82, 85, 74]
            }
          ]
        }
      },

      // 交通工具及游艇减免税费企业排行
      taxReductionRanking: {
        categories: ['海马汽车', '一汽海马', '海南马自达', '海口港务', '洋浦港务'],
        series: [
          {
            name: '减免税费(万元)',
            data: [2850, 2120, 1650, 1280, 920]
          }
        ]
      },

      // 自用生产设备减免税费企业排行
      equipmentTaxReduction: {
        categories: ['海南炼化', '中海油海南', '海航技术', '椰树集团', '海马汽车'],
        series: [
          {
            name: '减免税费(万元)',
            data: [5600, 4200, 3800, 2900, 2400]
          }
        ]
      }
    }

    return this.mockRequest(data)
  }

  // 获取实时统计数据
  async getRealTimeStats() {
    const data = {
      processing: {
        erpCompanies: Math.floor(Math.random() * 100) + 50,
        totalValue: Math.floor(Math.random() * 10000) + 10000,
        taxReduction: Math.floor(Math.random() * 5000) + 2000
      },
      transport: {
        vehicles: Math.floor(Math.random() * 500) + 1000,
        taxReduction: Math.floor(Math.random() * 2000) + 3000
      }
    }

    return this.mockRequest(data)
  }
}

export const mockDataService = new MockDataService()
