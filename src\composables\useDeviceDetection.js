import { ref, onMounted, onUnmounted } from 'vue'

export function useDeviceDetection() {
  const deviceType = ref('pc')
  const deviceClass = ref('pc')

  const updateDeviceType = () => {
    const width = window.innerWidth
    
    if (width <= 768) {
      deviceType.value = 'Mobile'
      deviceClass.value = 'mobile'
    } else if (width >= 1921) {
      deviceType.value = 'Wall'
      deviceClass.value = 'wall'
    } else {
      deviceType.value = 'PC'
      deviceClass.value = 'pc'
    }
  }

  onMounted(() => {
    updateDeviceType()
    window.addEventListener('resize', updateDeviceType)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceType)
  })

  return {
    deviceType,
    deviceClass
  }
}
