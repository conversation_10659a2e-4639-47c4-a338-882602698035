/**
 * 海关业务数据格式化工具函数
 */

// 数字格式化
export const formatNumber = (value, options = {}) => {
  const {
    precision = 2,
    unit = '',
    separator = true,
    prefix = '',
    suffix = ''
  } = options

  if (isNaN(value)) return '--'

  let formatted = value.toFixed(precision)
  
  if (separator) {
    formatted = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  return `${prefix}${formatted}${unit}${suffix}`
}

// 大数字格式化（万、亿）
export const formatLargeNumber = (value, precision = 2) => {
  if (isNaN(value)) return '--'
  
  if (value >= 100000000) {
    return `${(value / 100000000).toFixed(precision)}亿`
  } else if (value >= 10000) {
    return `${(value / 10000).toFixed(precision)}万`
  } else {
    return formatNumber(value, { precision })
  }
}

// 百分比格式化
export const formatPercentage = (value, precision = 2, showSign = false) => {
  if (isNaN(value)) return '--'
  
  const formatted = (value * 100).toFixed(precision)
  const sign = showSign && value > 0 ? '+' : ''
  return `${sign}${formatted}%`
}

// 货币格式化
export const formatCurrency = (value, currency = '¥', precision = 2) => {
  if (isNaN(value)) return '--'
  
  return `${currency}${formatNumber(value, { precision, separator: true })}`
}

// 日期格式化
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  const d = new Date(date)
  if (isNaN(d.getTime())) return '--'

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

// 相对时间格式化
export const formatRelativeTime = (date) => {
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else {
    return formatDate(d, 'YYYY-MM-DD')
  }
}

// 趋势格式化
export const formatTrend = (value, options = {}) => {
  const { showIcon = true, showSign = true, precision = 2 } = options
  
  if (isNaN(value)) {
    return { text: '--', icon: '', color: '#666' }
  }

  const absValue = Math.abs(value)
  const sign = showSign ? (value > 0 ? '+' : value < 0 ? '-' : '') : ''
  const text = `${sign}${absValue.toFixed(precision)}%`
  
  if (value > 0) {
    return {
      text,
      icon: showIcon ? '↗' : '',
      color: '#52c41a'
    }
  } else if (value < 0) {
    return {
      text,
      icon: showIcon ? '↘' : '',
      color: '#f5222d'
    }
  } else {
    return {
      text,
      icon: showIcon ? '→' : '',
      color: '#666'
    }
  }
}

// 海关业务数据格式化
export const formatCustomsValue = (value, type = 'currency') => {
  if (isNaN(value)) return '--'
  
  switch (type) {
    case 'currency':
      return formatLargeNumber(value) + '元'
    case 'weight':
      if (value >= 1000) {
        return formatLargeNumber(value / 1000) + '吨'
      }
      return formatNumber(value) + '公斤'
    case 'count':
      return formatLargeNumber(value) + '个'
    case 'enterprise':
      return formatLargeNumber(value) + '家'
    case 'vehicle':
      return formatLargeNumber(value) + '辆'
    default:
      return formatLargeNumber(value)
  }
}

// 企业类型格式化
export const formatEnterpriseType = (type) => {
  const typeMap = {
    'processing': '加工增值企业',
    'trading': '贸易企业',
    'logistics': '物流企业',
    'manufacturing': '制造企业',
    'service': '服务企业'
  }
  return typeMap[type] || type
}

// 地区名称格式化
export const formatRegionName = (code) => {
  const regionMap = {
    '460100': '海口市',
    '460200': '三亚市',
    '460300': '三沙市',
    '460400': '儋州市',
    '469001': '五指山市',
    '469002': '琼海市',
    '469005': '文昌市',
    '469006': '万宁市',
    '469007': '东方市',
    '469027': '乐东县',
    '469028': '陵水县'
  }
  return regionMap[code] || code
}

// 状态格式化
export const formatStatus = (status) => {
  const statusMap = {
    'active': '正常',
    'inactive': '停用',
    'pending': '待审核',
    'approved': '已审核',
    'rejected': '已拒绝',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 数据脱敏
export const maskSensitiveData = (data, type = 'phone') => {
  if (!data) return ''
  
  switch (type) {
    case 'phone':
      return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'email':
      return data.replace(/(.{2}).*(@.*)/, '$1****$2')
    case 'idCard':
      return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    case 'enterprise':
      // 企业名称脱敏
      if (data.length > 6) {
        return data.substring(0, 3) + '***' + data.substring(data.length - 3)
      }
      return data
    default:
      return data
  }
}
