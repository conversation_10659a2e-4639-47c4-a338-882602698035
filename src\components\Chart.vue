<template>
  <div class="chart-container" ref="chartRef"></div>
</template>

<script>
import * as echarts from 'echarts'
import { ref, onMounted, onUnmounted, watch } from 'vue'

export default {
  name: 'Chart',
  props: {
    option: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (chartRef.value) {
        chartInstance = echarts.init(chartRef.value)
        chartInstance.setOption(props.option)
      }
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', resizeChart)
    })

    watch(() => props.option, (newOption) => {
      if (chartInstance) {
        chartInstance.setOption(newOption)
      }
    }, { deep: true })

    return {
      chartRef
    }
  }
}
</script>
