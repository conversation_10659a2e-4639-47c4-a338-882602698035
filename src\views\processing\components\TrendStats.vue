<template>
  <div class="trend-stats" :class="theme">
    <!-- 数据卡片 -->
    <div class="stats-cards">
      <el-card v-for="stat in statsData" :key="stat.key" class="stat-card">
        <template #header>
          <div class="card-header">
            <span>{{ stat.title }}</span>
            <el-tag :type="stat.tagType" effect="dark">{{ stat.trend }}</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ stat.value }} {{ stat.unit }}</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'line',
                  data: stat.chartData,
                  smooth: true,
                  showSymbol: false,
                  lineStyle: { width: 3 },
                  areaStyle: {
                    opacity: 0.1
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>月度趋势分析</span>
            <el-tag type="success" effect="dark">同环比</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="monthlyAnalysisChartOption"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>产品类型趋势</span>
            <el-tag type="warning" effect="dark">月度统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="productTrendChartOption"
            autoresize
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores'
import { formatNumber } from '@/utils/format'

// 属性定义
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({
      averageValue: 0,
      growthRate: 0,
      yearForecast: 0,
      monthlyAnalysis: [],
      productTrend: []
    })
  }
})

// 主题状态
const themeStore = useThemeStore()
const theme = computed(() => themeStore.theme)

// 月份数据
const monthData = ['1月', '2月', '3月', '4月', '5月', '6月']

// 统计卡片数据
const statsData = computed(() => [
  {
    key: 'avg_production',
    title: '月均产值',
    value: formatNumber(props.data.averageValue),
    unit: '万元',
    trend: '稳定增长',
    tagType: 'success',
    chartData: props.data.monthlyAnalysis.map(item => item.value)
  },
  {
    key: 'growth_rate',
    title: '增长率',
    value: props.data.growthRate.toFixed(1),
    unit: '%',
    trend: props.data.growthRate > 0 ? '持续向好' : '需要关注',
    tagType: props.data.growthRate > 0 ? 'success' : 'warning',
    chartData: props.data.monthlyAnalysis.map(item => item.yearOverYear)
  },
  {
    key: 'year_estimate',
    title: '年度预计',
    value: formatNumber(props.data.yearForecast),
    unit: '万元',
    trend: `完成计划 ${((props.data.averageValue * 6 / props.data.yearForecast) * 100).toFixed(1)}%`,
    tagType: 'primary',
    chartData: props.data.monthlyAnalysis.map(item => item.forecast)
  }
])

// 月度趋势分析图表配置
const monthlyAnalysisChartOption = computed(() => ({
  tooltip: { 
    trigger: 'axis',
    formatter: params => {
      return params.map(param => {
        const value = param.seriesName === '产值' 
          ? `${formatNumber(param.value)} 万元`
          : `${param.value.toFixed(1)}%`
        return `${param.name}<br/>${param.seriesName}: ${value}`
      }).join('<br/>')
    }
  },
  legend: {
    data: ['产值', '同比增长', '环比增长']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: monthData,
    axisLabel: {
      interval: 0
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '产值(万元)',
      position: 'left',
      axisLabel: {
        formatter: value => formatNumber(value)
      }
    },
    {
      type: 'value',
      name: '增长率(%)',
      position: 'right',
      axisLabel: {
        formatter: '{value}%'
      }
    }
  ],
  series: [
    {
      name: '产值',
      type: 'bar',
      data: props.data.monthlyAnalysis.map(item => item.value),
      barWidth: '30%',
      yAxisIndex: 0,
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      },
      label: {
        show: true,
        position: 'top',
        formatter: value => formatNumber(value)
      }
    },
    {
      name: '同比增长',
      type: 'line',
      yAxisIndex: 1,
      data: props.data.monthlyAnalysis.map(item => item.yearOverYear),
      smooth: true,
      lineStyle: { width: 2 },
      label: {
        show: true,
        formatter: value => `${value.toFixed(1)}%`
      }
    },
    {
      name: '环比增长',
      type: 'line',
      yAxisIndex: 1,
      data: props.data.monthlyAnalysis.map(item => item.monthOverMonth),
      smooth: true,
      lineStyle: { width: 2 },
      label: {
        show: true,
        formatter: value => `${value.toFixed(1)}%`
      }
    }
  ]
}))

// 产品类型趋势图表配置
const productTrendChartOption = computed(() => ({
  tooltip: { 
    trigger: 'axis',
    formatter: params => {
      const total = params.reduce((sum, param) => sum + param.value, 0)
      return params.map(param => {
        const percentage = (param.value / total * 100).toFixed(1)
        return `${param.name}<br/>${param.seriesName}: ${formatNumber(param.value)} 万元 (${percentage}%)`
      }).join('<br/>')
    }
  },
  legend: {
    data: props.data.productTrend.map(item => item.name)
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: monthData,
    axisLabel: {
      interval: 0
    }
  },
  yAxis: {
    type: 'value',
    name: '产值(万元)',
    axisLabel: {
      formatter: value => formatNumber(value)
    }
  },
  series: props.data.productTrend.map(item => ({
    name: item.name,
    type: 'line',
    stack: 'Total',
    areaStyle: {},
    emphasis: { focus: 'series' },
    data: item.trend,
    label: {
      show: true,
      position: 'top',
      formatter: value => formatNumber(value)
    }
  }))
}))
</script>

<style scoped>
.trend-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.stat-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.chart {
  height: 100px;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--grid-gap);
  flex: 1;
}

.chart-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.chart-content {
  height: 300px;
}
</style>