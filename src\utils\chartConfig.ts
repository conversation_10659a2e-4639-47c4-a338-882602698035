import { EChartsOption } from 'echarts'

export const getBaseChartConfig = (): EChartsOption => {
  return {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#fff'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
}

export const getBarChartConfig = (data: any): EChartsOption => {
  return {
    ...getBaseChartConfig(),
    xAxis: {
      type: 'category',
      axisLine: {
        lineStyle: { color: '#fff' }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: { color: '#fff' }
      }
    },
    series: [{
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: '#4B8BF5'
      }
    }]
  }
}