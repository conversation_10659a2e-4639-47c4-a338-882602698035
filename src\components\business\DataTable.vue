<template>
  <base-card :loading="loading" :error="error" @retry="$emit('retry')">
    <div class="data-table">
      <table class="w-full">
        <thead>
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="{ width: column.width }"
              class="p-3 text-left font-medium text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700"
            >
              {{ column.title }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(row, index) in data"
            :key="index"
            class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              class="p-3 text-gray-600 dark:text-gray-400"
            >
              {{ column.formatter ? column.formatter(row[column.key]) : row[column.key] }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </base-card>
</template>

<script setup>
import BaseCard from '../base/Card.vue'

defineProps({
  columns: {
    type: Array,
    required: true,
    validator: (value) =>
      value.every((column) => column.key && column.title)
  },
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: [Error, String, null],
    default: null
  }
})

defineEmits(['retry'])
</script>

<style scoped>
.data-table {
  width: 100%;
  overflow-x: auto;
}
</style>