#!/bin/bash

# 系统监控配置
LOG_DIR="/var/log/port-leadership-window"
ALERT_EMAIL="<EMAIL>"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 创建日志目录
mkdir -p "$LOG_DIR"

# 系统资源监控
monitor_system_resources() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}')
    local memory_usage=$(free | grep Mem | awk '{print $3/$2 * 100.0}')
    local disk_usage=$(df -h / | awk '/\// {print $(NF-1)}' | sed 's/%//')

    echo "[$timestamp] CPU: ${cpu_usage}%, 内存: ${memory_usage}%, 磁盘: ${disk_usage}%" >> "$LOG_DIR/system_resources.log"

    # 资源告警阈值
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        send_alert "高CPU使用率告警" "CPU使用率: ${cpu_usage}%"
    fi

    if (( $(echo "$memory_usage > 85" | bc -l) )); then
        send_alert "高内存使用率告警" "内存使用率: ${memory_usage}%"
    fi
}

# 应用服务监控
monitor_application() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local app_status=$(pm2 list | grep "port-leadership-window" | awk '{print $10}')
    local error_count=$(grep -c "ERROR" "$LOG_DIR/application.log")

    echo "[$timestamp] 应用状态: $app_status, 错误数: $error_count" >> "$LOG_DIR/application_status.log"

    if [ "$app_status" != "online" ]; then
        send_alert "应用服务异常" "应用当前状态: $app_status"
        restart_application
    fi
}

# 发送告警通知
send_alert() {
    local subject="$1"
    local message="$2"

    echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    
    # 企业微信/钉钉告警（需要配置webhook）
    # curl "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY" \
    #      -H "Content-Type: application/json" \
    #      -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"$message\"}}"
}

# 重启应用
restart_application() {
    pm2 restart port-leadership-window
    send_alert "应用自动重启" "检测到服务异常，已自动重启"
}

# 日志轮转
rotate_logs() {
    find "$LOG_DIR" -name "*.log" -mtime +7 -delete
}

# 主监控流程
main() {
    echo -e "${GREEN}开始海关领导视窗系统监控...${NC}"

    monitor_system_resources
    monitor_application
    rotate_logs

    echo -e "${GREEN}监控任务完成${NC}"
}

# 每5分钟执行一次
while true; do
    main
    sleep 300
done 