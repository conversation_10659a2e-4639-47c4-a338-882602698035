import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGlobalState } from './useGlobalState'
import { useRoute } from 'vue-router'
import { DeviceType } from './useLayoutConfig'

// 设备断点配置
const breakpoints = {
  mobile: { max: 767 },
  pc: { min: 768, max: 1919 },
  wall: { min: 1920 }
}

/**
 * 使用设备适配器
 * @returns {object} 设备适配器对象
 */
export const useDeviceAdapter = () => {
  const route = useRoute()
  const { state } = useGlobalState()
  const windowWidth = ref(window.innerWidth)

  // 设备类型
  const deviceType = computed({
    get: () => state.device,
    set: (value) => {
      if (Object.values(DeviceType).includes(value)) {
        state.device = value
      }
    }
  })

  // 检测设备类型
  const detectDeviceType = () => {
    // 优先使用路由参数
    if (route.query.device && Object.values(DeviceType).includes(route.query.device)) {
      deviceType.value = route.query.device
      return
    }

    const width = windowWidth.value

    // 根据屏幕宽度判断设备类型
    if (width <= breakpoints.mobile.max) {
      deviceType.value = DeviceType.MOBILE
    } else if (width >= breakpoints.wall.min) {
      deviceType.value = DeviceType.WALL
    } else {
      deviceType.value = DeviceType.PC
    }
  }

  // 监听窗口大小变化
  const handleResize = () => {
    windowWidth.value = window.innerWidth
    detectDeviceType()
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize)
    detectDeviceType()
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    deviceType,
    windowWidth
  }
}