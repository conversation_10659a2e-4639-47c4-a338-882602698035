<template>
  <div class="business-chart relative">
    <!-- 加载状态 -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black/10">
      <div class="loading-spinner" />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="absolute inset-0 flex flex-col items-center justify-center bg-black/10">
      <p class="text-red-500 mb-2">{{ error.message || '加载失败' }}</p>
      <button
        class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors"
        @click="$emit('retry')"
      >
        重试
      </button>
    </div>

    <!-- 图表容器 -->
    <div ref="chartRef" class="w-full h-full" />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { useGlobalState } from '@/composables/useGlobalState'
import { useChartConfig } from '@/composables/useChartConfig'
import * as echarts from 'echarts'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => ['line', 'bar', 'pie'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  loading: Boolean,
  error: [Error, null]
})

const emit = defineEmits(['retry'])

// 获取全局状态
const { state } = useGlobalState()
const { device, theme } = state

// 获取图表配置
const { getConfig } = useChartConfig()

// 图表实例
const chartRef = ref(null)
let chart = null
let updateTimer = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁旧实例
  if (chart) {
    chart.dispose()
  }

  // 创建新实例
  chart = echarts.init(chartRef.value)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chart || !props.data) return

  // 防止频繁更新
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  updateTimer = setTimeout(() => {
    const baseConfig = getConfig(device.value, theme.value)
    const options = {
      ...baseConfig,
      series: [getSeriesConfig()]
    }

  // 根据图表类型设置特定配置
  switch (props.type) {
    case 'line':
      options.xAxis = {
        ...baseConfig.xAxis,
        type: 'category',
        data: props.data.xAxis || []
      }
      options.yAxis = {
        ...baseConfig.yAxis,
        type: 'value'
      }
      break
    case 'bar':
      options.xAxis = {
        ...baseConfig.xAxis,
        type: 'category',
        data: props.data.xAxis || []
      }
      options.yAxis = {
        ...baseConfig.yAxis,
        type: 'value'
      }
      break
    case 'pie':
      options.legend = {
        ...baseConfig.legend,
        orient: 'vertical',
        right: '5%',
        top: 'middle'
      }
      break
  }

    chart.setOption(options, true) // 使用 notMerge 参数，避免配置合并带来的性能开销
  }, 100) // 设置 100ms 的防抖延迟
}

// 获取系列配置
const getSeriesConfig = () => {
  const baseConfig = {
    type: props.type,
    data: props.type === 'pie' ? props.data.series : props.data.series?.[0]?.data || []
  }

  switch (props.type) {
    case 'line':
      return {
        ...baseConfig,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        name: props.data.series?.[0]?.name
      }
    case 'bar':
      return {
        ...baseConfig,
        barWidth: '40%',
        name: props.data.series?.[0]?.name
      }
    case 'pie':
      return {
        ...baseConfig,
        radius: ['50%', '70%'],
        center: ['40%', '50%'],
        label: {
          show: true,
          formatter: '{b}: {d}%'
        }
      }
    default:
      return baseConfig
  }
}

// 处理窗口大小变化
const handleResize = () => {
  chart?.resize()
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true }
)

// 监听主题变化
watch(
  () => theme.value,
  () => {
    updateChart()
  }
)

// 监听设备类型变化
watch(
  () => device.value,
  () => {
    updateChart()
  }
)

// 生命周期钩子
onMounted(() => {
  initChart()
  updateChart()
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  if (updateTimer) {
    clearTimeout(updateTimer)
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.business-chart {
  width: 100%;
  height: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--primary-color);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>