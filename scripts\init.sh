#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

# 检查Node.js版本
check_node_version() {
    local required_version="16.0.0"
    local current_version=$(node -v | sed 's/v//')
    
    if [ "$(printf '%s\n' "$required_version" "$current_version" | sort -V | head -n1)" = "$required_version" ]; then
        echo -e "${GREEN}Node.js版本符合要求${NC}"
    else
        echo -e "${RED}Node.js版本不符合要求，请安装${required_version}或更高版本${NC}"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${GREEN}正在安装项目依赖...${NC}"
    npm install
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}依赖安装成功${NC}"
    else
        echo -e "${RED}依赖安装失败${NC}"
        exit 1
    fi
}

# 环境配置
setup_environment() {
    # 复制环境配置文件
    if [ ! -f .env ]; then
        cp .env.example .env
        echo -e "${GREEN}已创建环境配置文件${NC}"
    fi
}

# 初始化Git钩子
setup_git_hooks() {
    mkdir -p .git/hooks
    cat > .git/hooks/pre-commit << EOL
#!/bin/sh
npm run lint
npm run test
EOL
    chmod +x .git/hooks/pre-commit
    echo -e "${GREEN}已配置Git提交前检查${NC}"
}

# 主初始化流程
main() {
    echo -e "${GREEN}开始初始化海关领导视窗系统${NC}"
    
    check_node_version
    install_dependencies
    setup_environment
    setup_git_hooks
    
    echo -e "${GREEN}初始化完成！${NC}"
    echo -e "${GREEN}可以使用 npm run dev 启动开发服务器${NC}"
}

main 