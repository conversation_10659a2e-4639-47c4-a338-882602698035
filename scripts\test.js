import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const runTests = async () => {
  console.log('🧪 开始执行海关领导视窗系统测试')
  
  const startTime = Date.now()
  
  const testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
  }
  
  const testFiles = [
    '../src/utils/performance.test.js',
    '../src/utils/request.test.js',
    '../src/api/processing.test.js',
    '../src/api/transport.test.js'
  ]
  
  for (const testFile of testFiles) {
    try {
      const fullPath = path.resolve(__dirname, testFile)
      
      if (!fs.existsSync(fullPath)) {
        console.warn(`⚠️ 测试文件不存在: ${testFile}`)
        testResults.skipped++
        continue
      }
      
      const test = await import(fullPath)
      
      if (typeof test.default === 'function') {
        const result = await test.default()
        
        testResults.total++
        if (result.success) {
          testResults.passed++
          console.log(`✅ 测试通过: ${testFile}`)
        } else {
          testResults.failed++
          console.error(`❌ 测试失败: ${testFile}`)
        }
      }
    } catch (error) {
      testResults.failed++
      console.error(`❌ 测试执行错误: ${testFile}`, error)
    }
  }
  
  const duration = Date.now() - startTime
  
  console.log('\n📊 测试报告:')
  console.log(`总测试数: ${testResults.total}`)
  console.log(`通过: ${testResults.passed}`)
  console.log(`失败: ${testResults.failed}`)
  console.log(`跳过: ${testResults.skipped}`)
  console.log(`耗时: ${duration}ms`)
  
  if (testResults.failed > 0) {
    console.error('❌ 存在测试失败')
    process.exit(1)
  }
}

runTests() 