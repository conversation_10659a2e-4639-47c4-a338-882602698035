<template>
  <div class="processing-base flex flex-col gap-4 p-4">
    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <stat-card
        v-for="stat in data?.stats"
        :key="stat.key"
        :title="stat.title"
        :value="stat.value"
        :unit="stat.unit"
        :trend="stat.trend"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-section" :class="chartsLayout">
      <!-- 月度趋势 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">月度趋势分析</h3>
        <business-chart
          type="line"
          :data="data?.monthlyTrend"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>

      <!-- 企业类型分布 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">企业类型分布</h3>
        <business-chart
          type="pie"
          :data="data?.enterpriseTypes"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>

      <!-- 区域分布 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">区域分布分析</h3>
        <business-chart
          type="bar"
          :data="data?.areaDistribution"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>

      <!-- TOP5产品 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">TOP5产品</h3>
        <data-table
          :columns="productColumns"
          :data="data?.topProducts"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useGlobalState } from '@/composables/useGlobalState'
import { useDataService } from '@/composables/useDataService'
import StatCard from '@/components/business/StatCard.vue'
import BusinessChart from '@/components/business/BusinessChart.vue'
import DataTable from '@/components/business/DataTable.vue'

const props = defineProps({
  chartsLayout: {
    type: String,
    default: 'grid-2-2'
  }
})

// 获取全局状态
const { state } = useGlobalState()
const isLoading = computed(() => state.isLoading)
const error = computed(() => state.error)

// 获取数据服务
const { loadData, refreshData } = useDataService()
const data = computed(() => state.processingData)

// 表格列配置
const productColumns = [
  { key: 'name', title: '产品名称', width: '200px' },
  {
    key: 'volume',
    title: '加工量',
    width: '150px',
    formatter: (value) => `${value}吨`
  },
  {
    key: 'valueAddedRate',
    title: '增值率',
    width: '120px',
    formatter: (value) => `${(value * 100).toFixed(1)}%`
  },
  {
    key: 'yearOverYear',
    title: '同比增长',
    width: '120px',
    formatter: (value) => `${(value * 100).toFixed(1)}%`
  }
]

// 处理重试
const handleRetry = () => {
  refreshData('processing')
}

// 初始化
onMounted(() => {
  loadData('processing')
})
</script>

<style scoped>
.processing-base {
  height: 100%;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1rem;
}

.charts-section {
  flex: 1;
  display: grid;
  gap: 1rem;
}

.charts-section.grid-2-2 {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.charts-section.grid-1-4 {
  grid-template-columns: 2fr 1fr;
  grid-template-rows: repeat(2, 1fr);
}

.chart-wrapper {
  background-color: var(--nav-bg-color);
}

.chart-title {
  color: var(--text-color);
}
</style>