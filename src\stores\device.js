import { defineStore } from 'pinia';

export const useDeviceStore = defineStore('device', {
  state: () => ({
    type: 'pc', // pc, mobile, large
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: false,
    isTablet: false,
    isDesktop: true
  }),

  actions: {
    updateDeviceType() {
      if (this.width >= 1920) {
        this.type = 'large';
        this.isDesktop = true;
        this.isTablet = false;
        this.isMobile = false;
      } else if (this.width <= 768) {
        this.type = 'mobile';
        this.isDesktop = false;
        this.isTablet = false;
        this.isMobile = true;
      } else if (this.width <= 1200) {
        this.type = 'pc';
        this.isDesktop = false;
        this.isTablet = true;
        this.isMobile = false;
      } else {
        this.type = 'pc';
        this.isDesktop = true;
        this.isTablet = false;
        this.isMobile = false;
      }
    },

    handleResize() {
      this.width = window.innerWidth;
      this.height = window.innerHeight;
      this.updateDeviceType();
    },

    initDeviceListener() {
      window.addEventListener('resize', this.handleResize);
      this.handleResize(); // 初始化时执行一次
    },

    removeDeviceListener() {
      window.removeEventListener('resize', this.handleResize);
    }
  }
});