import { computed } from 'vue'
import { useGlobalState } from './useGlobalState'

// 布局类型
export const LayoutType = {
  GRID: 'grid',
  CHART: 'chart'
}

// 设备类型
export const DeviceType = {
  PC: 'pc',
  MOBILE: 'mobile',
  WALL: 'wall'
}

// 布局配置表
const layoutConfig = {
  [DeviceType.PC]: {
    grid: {
      cols1: 'grid grid-cols-1',
      cols2: 'grid grid-cols-2',
      cols3: 'grid grid-cols-3',
      cols4: 'grid grid-cols-4'
    },
    chart: {
      height: 'h-[400px]',
      width: 'w-full'
    },
    spacing: {
      base: 'p-6',
      gap: 'gap-6'
    }
  },
  [DeviceType.MOBILE]: {
    grid: {
      cols1: 'grid grid-cols-1',
      cols2: 'grid grid-cols-2',
      cols3: 'grid grid-cols-1 md:grid-cols-3',
      cols4: 'grid grid-cols-2 md:grid-cols-4'
    },
    chart: {
      height: 'h-[300px]',
      width: 'w-full'
    },
    spacing: {
      base: 'p-4',
      gap: 'gap-4'
    }
  },
  [DeviceType.WALL]: {
    grid: {
      cols1: 'grid grid-cols-1',
      cols2: 'grid grid-cols-2',
      cols3: 'grid grid-cols-3',
      cols4: 'grid grid-cols-4'
    },
    chart: {
      height: 'h-[500px]',
      width: 'w-full'
    },
    spacing: {
      base: 'p-8',
      gap: 'gap-8'
    }
  }
}

/**
 * 使用布局配置的组合式函数
 * @returns {object} 布局配置相关的方法和状态
 */
export function useLayoutConfig() {
  const { state } = useGlobalState()
  
  // 当前设备类型
  const currentDevice = computed(() => state.device)
  
  // 获取当前设备的布局配置
  const getConfig = () => layoutConfig[currentDevice.value] || layoutConfig[DeviceType.PC]

  // 构建响应式类名
  const buildClasses = (type = LayoutType.GRID, options = {}) => {
    const layout = getConfig()
    const classes = []

    switch (type) {
      case LayoutType.GRID:
        classes.push(
          layout.grid[options.cols || 'cols2'],
          layout.spacing.base,
          layout.spacing.gap
        )
        break
      case LayoutType.CHART:
        classes.push(
          layout.chart.height,
          layout.chart.width
        )
        break
    }

    // 过滤掉 falsy 值并合并类名
    return classes.filter(Boolean).join(' ')
  }

  return {
    getConfig,
    buildClasses,
    currentDevice,
    LayoutType,
    DeviceType
  }
}