<template>
  <processing-base
    class="p-12 gap-12"
    charts-layout="grid-2-2"
    tables-layout="grid-2"
    :auto-refresh="true"
    refresh-interval="300000"
  />
</template>

<script setup>
import ProcessingBase from '../components/ProcessingBase.vue'
</script>

<style scoped>
:deep(.stats-section) {
  grid-template-columns: repeat(5, 1fr);
}

:deep(.chart-wrapper),
:deep(.table-wrapper) {
  padding: 2rem;
}

:deep(.chart-title),
:deep(.table-title) {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

:deep(.data-table th),
:deep(.data-table td) {
  font-size: 1.25rem;
  padding: 1.5rem;
}
</style>