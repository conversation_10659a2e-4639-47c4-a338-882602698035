<template>
  <ChartCard title="ERP联网企业统计" :options="chartOptions" />
</template>

<script setup>
import { computed, watch } from 'vue';
import ChartCard from '../../../components/ChartCard.vue';
import { useThemeStore } from '../../../stores/theme.js';

// 接收父组件传递的数据
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 获取主题状态
const themeStore = useThemeStore();

// 计算图表配置
const chartOptions = computed(() => {
  // 确保数据存在
  if (!props.data || !props.data.monthlyTrend) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    };
  }

  // 提取月份和数量数据
  const months = props.data.monthlyTrend.map(item => `${item.month}月`);
  const counts = props.data.monthlyTrend.map(item => item.count);
  
  // 计算同比增长率
  const growthRate = [];
  for (let i = 1; i < counts.length; i++) {
    const rate = counts[i] > 0 && counts[i-1] > 0 
      ? ((counts[i] - counts[i-1]) / counts[i-1] * 100).toFixed(1) 
      : 0;
    growthRate.push(rate);
  }
  growthRate.unshift(0); // 第一个月没有同比数据
  
  // 图表配置
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['企业数量', '同比增长率'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '企业数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '增长率(%)',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '企业数量',
        type: 'bar',
        barWidth: '40%',
        data: counts,
        itemStyle: {
          color: themeStore.isDark ? '#63e2b7' : '#67C23A'
        }
      },
      {
        name: '同比增长率',
        type: 'line',
        yAxisIndex: 1,
        data: growthRate,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: themeStore.isDark ? '#5d80f4' : '#409EFF'
        },
        itemStyle: {
          color: themeStore.isDark ? '#5d80f4' : '#409EFF'
        }
      }
    ]
  };
});
</script>