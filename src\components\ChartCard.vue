<template>
  <div class="chart-card">
    <div class="card-header">
      <h3>{{ title }}</h3>
    </div>
    <div class="card-body">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useChartConfig } from '../composables/useChartConfig'

export default {
  name: 'ChartCard',
  props: {
    title: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    chartType: {
      type: String,
      required: true,
      validator: (value) => ['bar', 'line', 'pie', 'horizontal-bar'].includes(value)
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const chartInstance = ref(null)
    const { getChartConfig } = useChartConfig()

    const initChart = async () => {
      if (!chartRef.value || !props.data) return

      await nextTick()
      
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }

      chartInstance.value = echarts.init(chartRef.value)
      const config = getChartConfig(props.chartType, props.data)
      chartInstance.value.setOption(config)
    }

    const resizeChart = () => {
      if (chartInstance.value) {
        chartInstance.value.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    watch(() => props.data, () => {
      initChart()
    }, { deep: true })

    watch(() => props.chartType, () => {
      initChart()
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart-card {
  background:
    linear-gradient(135deg, rgba(0, 212, 255, 0.12) 0%, rgba(0, 100, 200, 0.06) 100%),
    rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 2px solid rgba(0, 212, 255, 0.4);
  overflow: hidden;
  transition: all 0.4s ease;
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 20px rgba(0, 212, 255, 0.1);
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  background: linear-gradient(135deg, transparent, rgba(0, 212, 255, 0.08), transparent);
  pointer-events: none;
}

.chart-card:hover {
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow:
    0 12px 40px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 30px rgba(0, 212, 255, 0.2);
  transform: translateY(-2px) scale(1.01);
}

.card-header {
  padding: 18px 25px;
  background:
    linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 100, 200, 0.1) 100%),
    rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid rgba(0, 212, 255, 0.4);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, #0099cc, #00d4ff);
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
}

.card-header h3 {
  font-size: 1.2rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00d4ff 0%, #ffffff 50%, #00d4ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  text-align: center;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

.card-body {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
  min-height: 250px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chart-container {
    height: 250px;
  }
}

@media (min-width: 1921px) {
  .chart-container {
    height: 400px;
  }
  
  .card-header h3 {
    font-size: 1.3rem;
  }
}
</style>
