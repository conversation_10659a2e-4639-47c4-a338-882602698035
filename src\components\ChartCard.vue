<template>
  <div class="chart-card">
    <div class="card-header">
      <h3>{{ title }}</h3>
    </div>
    <div class="card-body">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useChartConfig } from '../composables/useChartConfig'

export default {
  name: 'ChartCard',
  props: {
    title: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    chartType: {
      type: String,
      required: true,
      validator: (value) => ['bar', 'line', 'pie', 'horizontal-bar'].includes(value)
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const chartInstance = ref(null)
    const { getChartConfig } = useChartConfig()

    const initChart = async () => {
      if (!chartRef.value || !props.data) return

      await nextTick()
      
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }

      chartInstance.value = echarts.init(chartRef.value)
      const config = getChartConfig(props.chartType, props.data)
      chartInstance.value.setOption(config)
    }

    const resizeChart = () => {
      if (chartInstance.value) {
        chartInstance.value.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    watch(() => props.data, () => {
      initChart()
    }, { deep: true })

    watch(() => props.chartType, () => {
      initChart()
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.card-header {
  padding: 15px 20px;
  background: rgba(0, 212, 255, 0.1);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.card-header h3 {
  font-size: 1.1rem;
  color: #00d4ff;
  margin: 0;
  text-align: center;
}

.card-body {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
  min-height: 250px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chart-container {
    height: 250px;
  }
}

@media (min-width: 1921px) {
  .chart-container {
    height: 400px;
  }
  
  .card-header h3 {
    font-size: 1.3rem;
  }
}
</style>
