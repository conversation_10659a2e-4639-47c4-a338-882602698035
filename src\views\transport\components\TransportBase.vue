<template>
  <div class="transport-base flex flex-col gap-4 p-4">
    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <stat-card
        v-for="stat in stats"
        :key="stat.key"
        :title="stat.title"
        :value="stat.value"
        :unit="stat.unit"
        :trend="stat.trend"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-section" :class="chartsLayout">
      <!-- 月度趋势 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">月度登记趋势</h3>
        <business-chart
          type="line"
          :data="monthlyRegistrations"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>

      <!-- 车辆类型分布 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">车辆类型分布</h3>
        <business-chart
          type="pie"
          :data="vehicleTypes"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>

      <!-- 区域分布 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">区域分布分析</h3>
        <business-chart
          type="bar"
          :data="regionDistribution"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>

      <!-- 维修分析 -->
      <div class="chart-wrapper rounded-lg shadow p-4">
        <h3 class="chart-title text-lg font-medium mb-4">维修分析</h3>
        <data-table
          :columns="maintenanceColumns"
          :data="maintenanceTop5"
          :loading="isLoading"
          :error="error"
          @retry="handleRetry"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useGlobalState } from '@/composables/useGlobalState'
import { useDataService } from '@/composables/useDataService'
import StatCard from '@/components/business/StatCard.vue'
import BusinessChart from '@/components/business/BusinessChart.vue'
import DataTable from '@/components/business/DataTable.vue'

const props = defineProps({
  chartsLayout: {
    type: String,
    default: 'grid-2-2'
  }
})

// 获取全局状态
const { state } = useGlobalState()
const isLoading = computed(() => state.isLoading)
const error = computed(() => state.error)

// 获取数据服务
const { loadData, refreshData } = useDataService()
const transportData = computed(() => state.transportData)

// 统计数据
const stats = computed(() => [
  {
    key: 'zeroTaxCars',
    title: '零关税车辆',
    value: transportData.value?.transportStats.zeroTaxCars.value,
    unit: transportData.value?.transportStats.zeroTaxCars.unit,
    trend: transportData.value?.transportStats.zeroTaxCars.trend
  },
  {
    key: 'postClosurePurchases',
    title: '封关后购置',
    value: transportData.value?.transportStats.postClosurePurchases.value,
    unit: transportData.value?.transportStats.postClosurePurchases.unit,
    trend: transportData.value?.transportStats.postClosurePurchases.trend
  },
  {
    key: 'maintenanceCount',
    title: '维修保养',
    value: transportData.value?.transportStats.maintenanceCount.value,
    unit: transportData.value?.transportStats.maintenanceCount.unit,
    trend: transportData.value?.transportStats.maintenanceCount.trend
  },
  {
    key: 'spareParts',
    title: '配件采购',
    value: transportData.value?.transportStats.spareParts.value,
    unit: transportData.value?.transportStats.spareParts.unit,
    trend: transportData.value?.transportStats.spareParts.trend
  }
])

// 图表数据
const vehicleTypes = computed(() => transportData.value?.vehicleTypes)
const monthlyRegistrations = computed(() => transportData.value?.monthlyRegistrations)
const regionDistribution = computed(() => transportData.value?.regionDistribution)
const maintenanceTop5 = computed(() => {
  const data = transportData.value?.maintenanceTop5
  if (!data) return []
  return data.labels.map((label, index) => ({
    name: label,
    count: data.datasets[0].data[index]
  }))
})

// 表格列配置
const maintenanceColumns = [
  { key: 'name', title: '维修点名称', width: '200px' },
  {
    key: 'count',
    title: '维修量',
    width: '150px',
    formatter: (value) => `${value}次`
  }
]

// 处理重试
const handleRetry = () => {
  refreshData('transport')
}

// 初始化
onMounted(() => {
  loadData('transport')
})
</script>

<style scoped>
.transport-base {
  height: 100%;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1rem;
}

.charts-section {
  flex: 1;
  display: grid;
  gap: 1rem;
}

.charts-section.grid-2-2 {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.charts-section.grid-1-4 {
  grid-template-columns: 2fr 1fr;
  grid-template-rows: repeat(2, 1fr);
}

.chart-wrapper {
  background-color: var(--nav-bg-color);
}

.chart-title {
  color: var(--text-color);
}
</style>