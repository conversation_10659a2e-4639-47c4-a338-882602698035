import { ref, readonly, provide, inject, computed } from 'vue'

// 全局状态键
const GLOBAL_STATE_KEY = Symbol('globalState')

// 初始状态
const initialState = {
  // 应用状态
  isLoading: false,
  error: null,
  device: 'pc',
  // 刷新间隔(毫秒)
  refreshInterval: 300000
}

// 创建全局状态
const createGlobalState = () => {
  const state = ref(initialState)

  // 计算属性：设备类型
  const device = computed({
    get: () => state.value.device,
    set: (value) => {
      if (['pc', 'mobile', 'wall'].includes(value)) {
        state.value.device = value
      }
    }
  })

  // 更新状态
  const setState = (key, value) => {
    if (!(key in state.value)) {
      throw new Error(`无效的状态键: ${key}`)
    }
    state.value[key] = value
  }

  // 设置错误信息
  const setError = (error) => {
    state.value.error = error
  }

  // 重置状态
  const resetState = () => {
    state.value = { ...initialState }
  }

  return {
    state: readonly(state),
    device,
    setState,
    setError,
    resetState
  }
}

// 提供全局状态
export const provideGlobalState = () => {
  const globalState = createGlobalState()
  provide(GLOBAL_STATE_KEY, globalState)
  return globalState
}

// 注入全局状态
export const useGlobalState = () => {
  const globalState = inject(GLOBAL_STATE_KEY)
  if (!globalState) {
    throw new Error('全局状态未提供')
  }
  return globalState
}