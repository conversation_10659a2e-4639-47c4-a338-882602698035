<template>
  <div class="vehicle-stats" :class="theme">
    <!-- 数据卡片 -->
    <div class="stats-cards">
      <el-card v-for="stat in stats" :key="stat.key" class="stat-card">
        <template #header>
          <div class="card-header">
            <span>{{ stat.title }}</span>
            <el-tag :type="stat.tagType" effect="dark">{{ stat.trend }}</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ stat.value }} {{ stat.unit }}</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'line',
                  data: stat.chartData,
                  smooth: true,
                  showSymbol: false,
                  lineStyle: { width: 3 },
                  areaStyle: {
                    opacity: 0.1
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>车辆品牌分布</span>
            <el-tag type="info" effect="dark">月度统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="{
              tooltip: { trigger: 'item' },
              series: [{
                type: 'pie',
                radius: ['40%', '70%'],
                data: data?.brandDistribution || [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }]
            }"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>维修点排名</span>
            <el-tag type="primary" effect="dark">月度统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="{
              tooltip: { trigger: 'axis' },
              xAxis: {
                type: 'value'
              },
              yAxis: {
                type: 'category',
                data: data?.maintenanceRanking?.map(item => item.name) || [],
                inverse: true
              },
              series: [{
                type: 'bar',
                data: data?.maintenanceRanking?.map(item => item.value) || [],
                barWidth: '60%',
                label: {
                  show: true,
                  position: 'right'
                }
              }]
            }"
            autoresize
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useThemeStore } from '@/stores'

// 属性定义
const props = defineProps({
  loading: Boolean,
  data: Object
})

// 主题状态
const themeStore = useThemeStore()
const theme = computed(() => themeStore.theme)

// 月份数据
const monthData = ['1月', '2月', '3月', '4月', '5月', '6月']

// 统计卡片数据
const stats = [
  {
    key: 'total_vehicles',
    title: '车辆总数',
    value: '12,568',
    unit: '辆',
    trend: '同比增长 8.9%',
    tagType: 'success',
    chartData: [1200, 1320, 1010, 1340, 900, 1200]
  },
  {
    key: 'maintenance_vehicles',
    title: '维修车辆',
    value: '3,256',
    unit: '辆',
    trend: '同比增长 5.3%',
    tagType: 'warning',
    chartData: [220, 182, 191, 234, 290, 330]
  },
  {
    key: 'spare_parts',
    title: '零配件采购',
    value: '15,689',
    unit: '件',
    trend: '同比增长 12.5%',
    tagType: 'danger',
    chartData: [1500, 1320, 1010, 1540, 1900, 1300]
  }
]
</script>

<style scoped>
.vehicle-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.stat-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.chart {
  height: 100px;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--grid-gap);
  flex: 1;
}

.chart-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.chart-content {
  height: 300px;
}
</style>