import { BaseDataAdapter } from './baseAdapter'

export class TransportDataAdapter extends BaseDataAdapter {
  constructor(rawData) {
    super(rawData)
  }

  // 获取总览数据
  getOverview() {
    const statistics = this._calculateStatistics(this.rawData, 'transportVolume')
    const typeDistribution = this._groupBy(this.rawData, 'transportType', 'transportVolume')
    
    return {
      ...statistics,
      typeDistribution,
      charts: {
        line: this._getLineChartData(),
        bar: this._getBarChartData(),
        pie: this._getPieChartData()
      }
    }
  }

  // 获取趋势数据
  getTrend() {
    const timeSeries = this._processTimeSeries(this.rawData, 'date', 'transportVolume')
    const growthRate = this._calculateGrowthRate(timeSeries)

    return {
      timeSeries,
      growthRate
    }
  }

  // 获取详细数据
  getDetails() {
    return {
      transportTypes: [...new Set(this.rawData.map(item => item.transportType))],
      records: this.rawData.map(item => ({
        date: item.date,
        type: item.transportType,
        volume: item.transportVolume,
        value: item.transportValue
      }))
    }
  }

  // 私有方法：折线图数据
  _getLineChartData() {
    return this._processTimeSeries(this.rawData, 'date', 'transportVolume')
  }

  // 私有方法：柱状图数据
  _getBarChartData() {
    return this._groupBy(this.rawData, 'transportType', 'transportVolume')
  }

  // 私有方法：饼图数据
  _getPieChartData() {
    return this._groupBy(this.rawData, 'transportType', 'transportVolume')
  }

  // 计算增长率
  _calculateGrowthRate(timeSeries) {
    if (timeSeries.length < 2) return 0

    const first = timeSeries[0].value
    const last = timeSeries[timeSeries.length - 1].value

    return ((last - first) / first * 100).toFixed(2)
  }
} 