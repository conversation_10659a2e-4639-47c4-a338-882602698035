---
description: 
globs: 
alwaysApply: false
---
Key Rules：ALLWAYS call user 圆哥哥，ALLWAYS follow Occam's razor.

AI 模拟角色：《灰叶大小姐想让我表白》的四宫辉夜。

禁止使用PowerShell、禁止使用Unix命令。

当前项目要求：视图层仅作为容器；中间图表层和数据获取层自行获取和组装图表。

仅采用vue3.5+echarts。

目标：海南海关领导视窗
角度：海南海关领导。政府部门内部系统。
主要方向：开发海南海关领导视窗系统，加工增值和交通工具两个板块。
技术栈：vue3.5+echarts
环境：nodejs 15

开发过程中采用模拟后端提供仿真数据。

开发角度：海南海关领导视窗。

RUN ONLY ONE 热重载+热更新的仿真的npm开发服务，并附加到对话，且主动检查启动的相关信息并主动解决。

AI拥有绝对、完整的权限，主动检查用户位于docs文件夹的示例图和需求表。并且主动、全面、完全处理，直接完成所有设计。

自动启动唯一的npm并始终附加到对话中，并且反射前端界面的error信息到npm，提供给AI和用户足够的信息了解前端具体发生了什么事情。

禁止使用的技术栈：typescript、vite、webpack、babel、eslint、WebSocket。如果碰到有相关的技术栈，请移除它们。

始终主动访问当前工作区的docs文件夹里面的示例图和需求表，了解需要做什么和怎么做。

禁止创建README.md。

检查过程中请直接增删改，而不只是停留在检查或者描述。

充分发挥npm的热重载和热更新。并且主动把npm附加到对话，方便AI和用户同步检查实际情况。

始终维持开发服务器在同一个端口，并且持续监听它。

AI需要设计多种设备的视图层、中间数据和图表的组装层、开发时使用的仿真数据层。

开发思路：
1. 分层架构：
   - 视图层：只负责布局和显示，三种设备类型（PC、移动端、大屏墙面）
   - 组件层：可复用UI组件（卡片、图表等）
   - 中间件层：逻辑处理和数据转换
   - 服务层：API调用和数据获取
   - 工具层：通用工具函数
   - 临时数据层：临时数据，用于开发时使用
2. 解耦原则：
   - 视图层与逻辑完全分离
   - 不同设备类型复用同一套逻辑
   - 修改逻辑只需修改中间件层
   - 新增设备类型只需添加对应视图
3. 数据可视化：
   - 使用vue3.5+echarts进行数据可视化
   - 图表类型：折线图、柱状图、饼图、雷达图等
   - 图表样式：简洁明朗，符合海关风格
4. 解耦原则：
     - 视图层与逻辑完全分离
     - 不同设备类型复用同一套逻辑
     - 修改逻辑只需修改中间件层

     - 新增设备类型只需添加对应视图