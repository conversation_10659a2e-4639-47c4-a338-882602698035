/* 主题变量 */
:root {
  /* 主题色 */
  --el-color-primary: #409eff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;

  /* 亮色主题变量 */
  --light-primary: #409eff;
  --light-success: #67c23a;
  --light-warning: #e6a23c;
  --light-danger: #f56c6c;
  --light-info: #909399;
  --light-background: #f0f2f5;
  --light-text: #303133;
  --light-border: #dcdfe6;
  --light-component-bg: #ffffff;
  
  /* 暗色主题变量 */
  --dark-primary: #409eff;
  --dark-success: #67c23a;
  --dark-warning: #e6a23c;
  --dark-danger: #f56c6c;
  --dark-info: #909399;
  --dark-background: #0d1117;
  --dark-text: #e0e0e0;
  --dark-border: #30363d;
  --dark-component-bg: #1a1f25;

  /* 背景色 */
  --app-bg-color: var(--light-background);
  --app-bg-color-light: #ffffff;
  --app-bg-color-dark: #001529;
  --app-header-bg: #001529;

  /* 文本色 */
  --app-text-color: var(--light-text);
  --app-text-color-light: #606266;
  --app-text-color-lighter: #909399;
  --app-text-color-white: #ffffff;

  /* 边框色 */
  --app-border-color: var(--light-border);
  --app-border-color-light: #e4e7ed;

  /* 组件背景色 */
  --app-component-bg: var(--light-component-bg);
  --app-component-bg-dark: #1f2937;

  /* 内容边距 */
  --app-content-padding: 20px;

  /* 卡片阴影 */
  --app-card-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  --app-card-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 图表高度 */
  --app-chart-height: 300px;
  
  /* 内容间距 */
  --content-gap: 20px;
  --grid-gap: 16px;
}

/* 设备适配 */
.device-pc {
  --app-content-padding: 20px;
}

.device-mobile {
  --app-content-padding: 10px;
}

.device-large {
  --app-content-padding: 30px;
}

/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  background: #181c23;
  color: #fff;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
}

#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 布局样式 */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  height: 60px;
  background-color: var(--app-header-bg);
  color: var(--app-text-color-white);
  display: flex;
  align-items: center;
  padding: 0 var(--app-content-padding);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: background-color 0.3s ease;
}

.app-main {
  flex: 1;
  padding: var(--app-content-padding);
  transition: background-color 0.3s ease;
}

/* 图表卡片样式 */
.chart-card {
  background-color: var(--app-component-bg);
  border-radius: 8px;
  box-shadow: var(--app-card-shadow);
  padding: 20px;
  margin-bottom: 20px;
  transition: box-shadow 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chart-card__header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-card__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--app-text-color);
  margin: 0;
  transition: color 0.3s ease;
}

.chart-card__content {
  height: var(--app-chart-height);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.stats-card {
  flex: 1;
  min-width: 180px;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-color 0.3s ease;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  text-align: center;
  padding: 10px 0;
  transition: color 0.3s ease;
}

.card-footer {
  text-align: center;
  font-size: 14px;
  color: var(--app-text-color-lighter);
  margin-top: 5px;
  transition: color 0.3s ease;
}

.trend-text {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: rgba(103, 194, 58, 0.1);
  color: var(--light-success);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 图表容器样式 */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 图表行样式 */
.charts-row {
  display: flex;
  gap: 20px;
  margin-bottom: 0;
}

.charts-row > * {
  flex: 1;
}

/* 深色主题样式 */
[data-theme="dark"] {
  --app-bg-color: var(--dark-background);
  --app-component-bg: var(--dark-component-bg);
  --app-text-color: var(--dark-text);
  --app-text-color-light: #9ca3af;
  --app-border-color: var(--dark-border);
  --app-card-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  
  background-color: var(--dark-background);
  color: var(--dark-text);
}

[data-theme="dark"] .el-card {
  background-color: var(--dark-component-bg);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

[data-theme="dark"] .el-card__header {
  border-color: var(--dark-border);
}

[data-theme="dark"] .el-table {
  background-color: var(--dark-component-bg);
  color: var(--dark-text);
}

[data-theme="dark"] .el-table th,
[data-theme="dark"] .el-table tr,
[data-theme="dark"] .el-table td {
  background-color: var(--dark-component-bg);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

[data-theme="dark"] .chart-card {
  background-color: var(--dark-component-bg);
}

[data-theme="dark"] .chart-card__title {
  color: var(--dark-text);
}

[data-theme="dark"] .trend-text {
  background-color: rgba(103, 194, 58, 0.2);
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }
  
  .charts-row {
    flex-direction: column;
  }
  
  .stats-card {
    min-width: 100%;
  }
  
  .chart-card__content {
    height: 250px;
    --app-chart-height: 250px;
  }
}

@media screen and (min-width: 1920px) {
  .stats-cards {
    gap: 20px;
  }
  
  .charts-container {
    gap: 30px;
  }
  
  .charts-row {
    gap: 30px;
  }
  
  .chart-card__content {
    height: 400px;
    --app-chart-height: 400px;
  }
  
  .card-value {
    font-size: 28px;
  }
  
  .card-footer {
    font-size: 16px;
  }
}

::-webkit-scrollbar {
  width: 8px;
  background: #232a36;
}
::-webkit-scrollbar-thumb {
  background: #2c3440;
  border-radius: 4px;
}
button {
  font-family: inherit;
  font-size: 14px;
  transition: background 0.2s;
}