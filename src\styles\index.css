/**
 * 海南海关领导视窗系统 - 全局样式
 * 统一深蓝色科技风格设计
 */

/* CSS变量定义 */
:root {
  /* 海关主色调 */
  --customs-primary: #1890ff;
  --customs-primary-light: #40a9ff;
  --customs-primary-dark: #096dd9;
  --customs-secondary: #13c2c2;

  /* 功能色彩 */
  --customs-success: #52c41a;
  --customs-warning: #faad14;
  --customs-error: #f5222d;
  --customs-info: #722ed1;

  /* 背景色系 */
  --customs-bg-primary: #0a0e27;
  --customs-bg-secondary: #1a1f3a;
  --customs-bg-tertiary: #0f1419;
  --customs-bg-card: rgba(15, 23, 42, 0.6);
  --customs-bg-header: rgba(15, 23, 42, 0.8);
  --customs-bg-overlay: rgba(10, 14, 39, 0.9);

  /* 文字色系 */
  --customs-text-primary: #ffffff;
  --customs-text-secondary: rgba(255, 255, 255, 0.8);
  --customs-text-tertiary: rgba(255, 255, 255, 0.6);
  --customs-text-disabled: rgba(255, 255, 255, 0.4);
  --customs-text-inverse: #000000;

  /* 边框色系 */
  --customs-border-primary: rgba(59, 130, 246, 0.2);
  --customs-border-secondary: rgba(59, 130, 246, 0.3);
  --customs-border-hover: rgba(59, 130, 246, 0.4);
  --customs-border-active: rgba(59, 130, 246, 0.6);

  /* 阴影效果 */
  --customs-shadow-sm: 0 2px 8px rgba(59, 130, 246, 0.1);
  --customs-shadow-md: 0 4px 16px rgba(59, 130, 246, 0.2);
  --customs-shadow-lg: 0 8px 25px rgba(59, 130, 246, 0.3);
  --customs-shadow-xl: 0 12px 32px rgba(59, 130, 246, 0.4);

  /* 圆角规范 */
  --customs-radius-xs: 2px;
  --customs-radius-sm: 4px;
  --customs-radius-md: 8px;
  --customs-radius-lg: 12px;
  --customs-radius-xl: 16px;
  --customs-radius-xxl: 20px;

  /* 间距规范 */
  --customs-space-xs: 4px;
  --customs-space-sm: 8px;
  --customs-space-md: 16px;
  --customs-space-lg: 24px;
  --customs-space-xl: 32px;
  --customs-space-xxl: 48px;
  --customs-space-xxxl: 64px;

  /* 字体规范 */
  --customs-font-xs: 12px;
  --customs-font-sm: 14px;
  --customs-font-md: 16px;
  --customs-font-lg: 18px;
  --customs-font-xl: 20px;
  --customs-font-xxl: 24px;
  --customs-font-xxxl: 28px;
  --customs-font-display: 32px;

  /* 行高规范 */
  --customs-line-height-tight: 1.2;
  --customs-line-height-normal: 1.5;
  --customs-line-height-loose: 1.8;

  /* 动画时长 */
  --customs-duration-fast: 0.15s;
  --customs-duration-normal: 0.3s;
  --customs-duration-slow: 0.5s;
  --customs-duration-slower: 0.8s;

  /* 动画缓动 */
  --customs-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --customs-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --customs-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index层级 */
  --customs-z-base: 1;
  --customs-z-dropdown: 1000;
  --customs-z-sticky: 1020;
  --customs-z-fixed: 1030;
  --customs-z-modal: 1050;
  --customs-z-popover: 1060;
  --customs-z-tooltip: 1070;
  --customs-z-toast: 1080;
  --customs-z-loading: 1090;

  /* 断点规范 */
  --customs-breakpoint-xs: 480px;
  --customs-breakpoint-sm: 576px;
  --customs-breakpoint-md: 768px;
  --customs-breakpoint-lg: 992px;
  --customs-breakpoint-xl: 1200px;
  --customs-breakpoint-xxl: 1600px;
  --customs-breakpoint-xxxl: 1920px;
}

/* 全局重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* HTML基础设置 */
html {
  font-size: 16px;
  line-height: var(--customs-line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

/* Body基础样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, var(--customs-bg-primary) 0%, var(--customs-bg-secondary) 50%, var(--customs-bg-tertiary) 100%);
  color: var(--customs-text-primary);
  overflow-x: hidden;
  min-height: 100vh;
  font-size: var(--customs-font-sm);
  line-height: var(--customs-line-height-normal);
}

/* 背景装饰效果 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* App容器 */
#app {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 海关系统通用组件样式 */

/* 按钮样式 */
.customs-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--customs-space-sm);
  padding: var(--customs-space-sm) var(--customs-space-md);
  border: 1px solid var(--customs-border-primary);
  border-radius: var(--customs-radius-md);
  background: var(--customs-bg-card);
  color: var(--customs-text-primary);
  font-size: var(--customs-font-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--customs-duration-normal) var(--customs-ease-in-out);
  backdrop-filter: blur(10px);
}

.customs-btn:hover {
  background: var(--customs-primary);
  border-color: var(--customs-primary);
  box-shadow: var(--customs-shadow-md);
  transform: translateY(-1px);
}

.customs-btn:active {
  transform: translateY(0);
}

.customs-btn.primary {
  background: linear-gradient(135deg, var(--customs-primary), var(--customs-primary-dark));
  border-color: var(--customs-primary);
}

.customs-btn.secondary {
  background: linear-gradient(135deg, var(--customs-secondary), #0d9488);
  border-color: var(--customs-secondary);
}

/* 卡片样式 */
.customs-card {
  background: var(--customs-bg-card);
  backdrop-filter: blur(10px);
  border: 1px solid var(--customs-border-primary);
  border-radius: var(--customs-radius-lg);
  padding: var(--customs-space-lg);
  transition: all var(--customs-duration-normal) var(--customs-ease-in-out);
  position: relative;
  overflow: hidden;
}

.customs-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--customs-primary), var(--customs-secondary), var(--customs-success));
}

.customs-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--customs-shadow-lg);
  border-color: var(--customs-border-hover);
}

/* 标题样式 */
.customs-title {
  font-size: var(--customs-font-xxl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--customs-primary), var(--customs-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--customs-space-md);
}

.customs-subtitle {
  font-size: var(--customs-font-md);
  color: var(--customs-text-secondary);
  margin-bottom: var(--customs-space-lg);
}

/* 状态指示器 */
.customs-status {
  display: inline-flex;
  align-items: center;
  gap: var(--customs-space-sm);
  padding: var(--customs-space-xs) var(--customs-space-sm);
  border-radius: var(--customs-radius-sm);
  font-size: var(--customs-font-xs);
  font-weight: 500;
}

.customs-status.online {
  background: rgba(82, 196, 26, 0.2);
  color: var(--customs-success);
}

.customs-status.offline {
  background: rgba(245, 34, 45, 0.2);
  color: var(--customs-error);
}

.customs-status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.customs-status.online .customs-status-dot {
  background: var(--customs-success);
}

.customs-status.offline .customs-status-dot {
  background: var(--customs-error);
}

/* 动画定义 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.05);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--customs-primary);
  }
  50% {
    box-shadow: 0 0 20px var(--customs-primary), 0 0 30px var(--customs-primary);
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn var(--customs-duration-normal) var(--customs-ease-out);
}

.slide-in-left {
  animation: slideInFromLeft var(--customs-duration-normal) var(--customs-ease-out);
}

.slide-in-right {
  animation: slideInFromRight var(--customs-duration-normal) var(--customs-ease-out);
}

.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

/* 加载动画 */
.customs-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--customs-border-primary);
  border-top: 2px solid var(--customs-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.customs-loading.large {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--customs-space-xs); }
.mb-2 { margin-bottom: var(--customs-space-sm); }
.mb-3 { margin-bottom: var(--customs-space-md); }
.mb-4 { margin-bottom: var(--customs-space-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--customs-space-xs); }
.mt-2 { margin-top: var(--customs-space-sm); }
.mt-3 { margin-top: var(--customs-space-md); }
.mt-4 { margin-top: var(--customs-space-lg); }

/* 响应式设计 */

/* 超大屏幕 (≥1920px) */
@media screen and (min-width: 1920px) {
  :root {
    --customs-font-xs: 14px;
    --customs-font-sm: 16px;
    --customs-font-md: 18px;
    --customs-font-lg: 20px;
    --customs-font-xl: 24px;
    --customs-font-xxl: 28px;
    --customs-font-xxxl: 32px;
    --customs-font-display: 36px;
  }

  .customs-card {
    padding: var(--customs-space-xl);
  }

  .dashboard-header {
    height: 100px;
  }

  .dashboard-title {
    font-size: var(--customs-font-xxxl);
  }
}

/* 大屏幕 (≥1200px) */
@media screen and (min-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .indicators-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* 中等屏幕 (768px - 1199px) */
@media screen and (max-width: 1199px) {
  .charts-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-card-wide {
    grid-column: span 2;
  }
}

/* 平板屏幕 (768px - 991px) */
@media screen and (max-width: 991px) {
  .dashboard-header {
    height: 70px;
  }

  .dashboard-title {
    font-size: var(--customs-font-xl);
  }

  .header-info {
    gap: var(--customs-space-md);
    font-size: var(--customs-font-xs);
  }

  .indicators-section,
  .charts-section {
    padding: var(--customs-space-md);
  }

  .customs-card {
    padding: var(--customs-space-md);
  }
}

/* 小屏幕 (≤768px) */
@media screen and (max-width: 768px) {
  .dashboard-header {
    height: 60px;
  }

  .header-content {
    padding: 0 var(--customs-space-md);
  }

  .dashboard-title {
    font-size: var(--customs-font-lg);
  }

  .title-icon {
    font-size: var(--customs-font-xl);
  }

  .header-info {
    gap: var(--customs-space-sm);
    font-size: var(--customs-font-xs);
  }

  .indicators-section,
  .charts-section {
    padding: var(--customs-space-md);
  }

  .indicators-grid {
    grid-template-columns: 1fr;
    gap: var(--customs-space-md);
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: var(--customs-space-md);
  }

  .chart-card-wide {
    grid-column: span 1;
  }

  .customs-card {
    padding: var(--customs-space-md);
  }
}

/* 超小屏幕 (≤480px) */
@media screen and (max-width: 480px) {
  .dashboard-header {
    height: 50px;
  }

  .header-content {
    padding: 0 var(--customs-space-sm);
  }

  .dashboard-title {
    font-size: var(--customs-font-md);
  }

  .indicators-section,
  .charts-section {
    padding: var(--customs-space-sm);
  }

  .indicators-grid,
  .charts-grid {
    gap: var(--customs-space-sm);
  }

  .customs-card {
    padding: var(--customs-space-sm);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--customs-bg-secondary);
  border-radius: var(--customs-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--customs-border-secondary);
  border-radius: var(--customs-radius-sm);
  transition: background var(--customs-duration-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--customs-border-hover);
}

::-webkit-scrollbar-corner {
  background: var(--customs-bg-secondary);
}

/* Firefox滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--customs-border-secondary) var(--customs-bg-secondary);
}

/* 选择文本样式 */
::selection {
  background: var(--customs-primary);
  color: var(--customs-text-primary);
}

::-moz-selection {
  background: var(--customs-primary);
  color: var(--customs-text-primary);
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--customs-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 按钮和表单元素基础样式 */
button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
}

/* 链接样式 */
a {
  color: var(--customs-primary);
  text-decoration: none;
  transition: color var(--customs-duration-normal);
}

a:hover {
  color: var(--customs-primary-light);
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  text-align: left;
  padding: var(--customs-space-sm);
  border-bottom: 1px solid var(--customs-border-primary);
}

th {
  font-weight: 600;
  color: var(--customs-text-secondary);
  background: var(--customs-bg-card);
}

/* 代码样式 */
code,
pre {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  background: var(--customs-bg-card);
  border-radius: var(--customs-radius-sm);
}

code {
  padding: 2px 4px;
  font-size: 0.875em;
}

pre {
  padding: var(--customs-space-md);
  overflow-x: auto;
}

/* 打印样式 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .dashboard-header,
  .loading-overlay {
    display: none !important;
  }

  .customs-card {
    border: 1px solid #ccc !important;
    break-inside: avoid;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --customs-border-primary: rgba(255, 255, 255, 0.5);
    --customs-border-secondary: rgba(255, 255, 255, 0.7);
    --customs-text-secondary: rgba(255, 255, 255, 0.9);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}