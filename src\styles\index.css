/* 海关大屏简洁样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
  background: #0a1428;
  color: #ffffff;
  overflow-x: hidden;
}

#app {
  width: 100vw;
  height: 100vh;
}

/* 基础卡片样式 */
.card {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 300px;
}

/* 布局工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-4 { gap: 16px; }
.p-4 { padding: 16px; }
.m-4 { margin: 16px; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.text-center { text-align: center; }
