@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;

  --bg-color: #f0f2f5;
  --nav-bg-color: #ffffff;
  --wall-bg-color: #0a1929;
  --hover-color: rgba(0, 0, 0, 0.04);
  --active-color: rgba(24, 144, 255, 0.1);

  --text-color: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.45);
  --text-disabled: rgba(0, 0, 0, 0.25);

  --border-color: #d9d9d9;
  --border-secondary: #f0f0f0;

  --chart-color-1: #1890ff;
  --chart-color-2: #2fc25b;
  --chart-color-3: #facc14;
  --chart-color-4: #223273;
  --chart-color-5: #8543e0;
  --chart-color-6: #13c2c2;
  --chart-color-7: #3436c7;
  --chart-color-8: #f04864;
  --chart-color-9: #748cb1;
  --chart-color-10: #ff9845;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

::-webkit-scrollbar {
  width: 0.375rem;
  height: 0.375rem;
}

::-webkit-scrollbar-track {
  background-color: rgb(229 231 235);
  border-radius: 9999px;
}

.dark ::-webkit-scrollbar-track {
  background-color: rgb(55 65 81);
}

::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #096dd9;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border-width: 3px;
  border-color: var(--primary-color);
  border-top-color: transparent;
  border-radius: 9999px;
  animation: spin 1s linear infinite;
}

.chart-wrapper {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  padding: 1rem;
}

.dark .chart-wrapper {
  background-color: rgb(31 41 55);
}

.table-wrapper {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  padding: 1rem;
}

.dark .table-wrapper {
  background-color: rgb(31 41 55);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
  border-bottom-width: 1px;
  border-color: rgb(229 231 235);
}

.dark .data-table th,
.dark .data-table td {
  border-color: rgb(55 65 81);
}

.data-table th {
  font-weight: 500;
  color: rgb(55 65 81);
  background-color: rgb(249 250 251);
}

.dark .data-table th {
  color: rgb(209 213 219);
  background-color: rgb(17 24 39);
}

.data-table td {
  color: rgb(75 85 99);
}

.dark .data-table td {
  color: rgb(156 163 175);
}

.pc-layout {
  padding: 2rem;
  gap: 2rem;
}

.mobile-layout {
  padding: 1rem;
  gap: 1rem;
}

.wall-layout {
  padding: 3rem;
  gap: 3rem;
}

.grid-2-2 {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.grid-1-4 {
  display: grid;
  grid-template-columns: 2fr 1fr;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}