const express = require('express');
const path = require('path');
const app = express();
const PORT = 3000;

// 提供静态文件
app.use(express.static('public'));

// 模拟API数据
app.get('/api/data', (req, res) => {
  res.json({
    processing: {
      overview: {
        totalValue: 12500,
        enterpriseCount: 45,
        growthRate: 15.8
      },
      regionDistribution: [
        { name: '海口海关', value: 35 },
        { name: '洋浦海关', value: 25 },
        { name: '三亚海关', value: 20 },
        { name: '马村海关', value: 15 },
        { name: '其他', value: 5 }
      ],
      monthlyTrend: [
        { month: '1月', value: 800 },
        { month: '2月', value: 950 },
        { month: '3月', value: 1100 },
        { month: '4月', value: 1250 },
        { month: '5月', value: 1400 },
        { month: '6月', value: 1600 }
      ],
      topEnterprises: [
        { name: '海南钢铁', value: 2.5 },
        { name: '洋浦石化', value: 2.1 },
        { name: '三亚制造', value: 1.8 },
        { name: '海口科技', value: 1.5 },
        { name: '文昌航天', value: 1.2 }
      ]
    },
    transport: {
      overview: {
        zeroTariffImports: 1250,
        vehicleTypes: 8,
        totalValue: 8900
      },
      vehicleDistribution: [
        { name: '小汽车', value: 45 },
        { name: '货车', value: 25 },
        { name: '客车', value: 15 },
        { name: '特种车', value: 10 },
        { name: '其他', value: 5 }
      ],
      monthlyTrend: [
        { month: '1月', value: 600 },
        { month: '2月', value: 720 },
        { month: '3月', value: 850 },
        { month: '4月', value: 980 },
        { month: '5月', value: 1100 },
        { month: '6月', value: 1250 }
      ],
      taxExemption: {
        enterpriseRanking: [
          { name: '海南汽贸', value: 450 },
          { name: '洋浦物流', value: 380 },
          { name: '三亚运输', value: 320 },
          { name: '海口货运', value: 280 },
          { name: '文昌配送', value: 240 }
        ]
      }
    }
  });
});

app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
});
