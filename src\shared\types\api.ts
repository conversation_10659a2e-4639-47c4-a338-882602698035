/**
 * API相关类型定义
 */

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp: number
}

// 分页请求参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应数据
export interface PaginatedData<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 查询参数类型
export interface QueryParams {
  keyword?: string
  startDate?: string
  endDate?: string
  region?: string
  type?: string
  status?: string
  [key: string]: any
}

// 请求配置类型
export interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  retry?: number
  cache?: boolean
  cacheTTL?: number
}

// 错误响应类型
export interface ApiError {
  code: number
  message: string
  details?: any
  timestamp: number
  path?: string
}

// 文件上传类型
export interface FileUpload {
  file: File
  name: string
  type: string
  size: number
  progress?: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

// 导出请求类型
export interface ExportRequest {
  format: 'excel' | 'pdf' | 'csv'
  filename: string
  data?: any
  template?: string
  filters?: QueryParams
}

// 统计查询参数
export interface StatsQueryParams extends QueryParams {
  dimension: 'time' | 'region' | 'category' | 'enterprise'
  granularity?: 'day' | 'week' | 'month' | 'quarter' | 'year'
  metrics: string[]
}

// 业务数据API类型
export interface BusinessDataApi {
  // 加工增值相关
  getProcessingStats: (params: StatsQueryParams) => Promise<ApiResponse>
  getGoodsStatistics: (params: QueryParams) => Promise<ApiResponse>
  getTaxExemptionData: (params: QueryParams) => Promise<ApiResponse>
  getEnterpriseRanking: (params: QueryParams) => Promise<ApiResponse>
  getRegionDistribution: (params: QueryParams) => Promise<ApiResponse>
  
  // 交通运输工具相关
  getTransportStats: (params: StatsQueryParams) => Promise<ApiResponse>
  getZeroTariffData: (params: QueryParams) => Promise<ApiResponse>
  getDutyFreeVehicles: (params: QueryParams) => Promise<ApiResponse>
  getSparePartsData: (params: QueryParams) => Promise<ApiResponse>
  
  // 通用查询
  getBusinessIndicators: (params: QueryParams) => Promise<ApiResponse>
  getTimeSeriesData: (params: StatsQueryParams) => Promise<ApiResponse>
  exportData: (request: ExportRequest) => Promise<Blob>
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'data_update' | 'system_notification' | 'error' | 'heartbeat'
  payload: any
  timestamp: number
  id?: string
}

// 实时数据更新类型
export interface RealTimeUpdate {
  module: string
  data: any
  timestamp: number
  changeType: 'create' | 'update' | 'delete'
}

// 缓存配置类型
export interface CacheConfig {
  key: string
  ttl: number // 生存时间（秒）
  tags?: string[]
  version?: string
}

// 请求拦截器类型
export interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  onResponse?: (response: ApiResponse) => ApiResponse | Promise<ApiResponse>
  onError?: (error: ApiError) => Promise<ApiError>
}

// API客户端配置
export interface ApiClientConfig {
  baseURL: string
  timeout: number
  retryTimes: number
  retryDelay: number
  headers: Record<string, string>
  interceptors?: RequestInterceptor[]
  cache?: {
    enabled: boolean
    defaultTTL: number
    storage: 'memory' | 'localStorage' | 'sessionStorage'
  }
}
