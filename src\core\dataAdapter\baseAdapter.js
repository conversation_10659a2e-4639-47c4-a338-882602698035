export class BaseDataAdapter {
  constructor(rawData = []) {
    this.rawData = this._validateData(rawData)
  }

  // 数据验证
  _validateData(data) {
    if (!Array.isArray(data)) {
      console.warn('Invalid data format: Expected an array')
      return []
    }
    return data.filter(this._isValidItem)
  }

  // 验证单个数据项
  _isValidItem(item) {
    return item && typeof item === 'object'
  }

  // 安全获取数据属性
  _safeGet(obj, path, defaultValue = null) {
    return path.split('.').reduce((acc, key) => 
      acc && acc[key] !== undefined ? acc[key] : defaultValue, obj)
  }

  // 计算统计信息
  _calculateStatistics(data, valueKey) {
    if (!data.length) return { 
      total: 0, 
      average: 0, 
      max: 0, 
      min: 0 
    }

    const values = data.map(item => this._safeGet(item, valueKey, 0))
    
    return {
      total: values.reduce((sum, val) => sum + val, 0),
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      max: Math.max(...values),
      min: Math.min(...values)
    }
  }

  // 时间序列处理
  _processTimeSeries(data, dateKey, valueKey) {
    return data
      .sort((a, b) => new Date(a[dateKey]) - new Date(b[dateKey]))
      .map(item => ({
        date: item[dateKey],
        value: this._safeGet(item, valueKey, 0)
      }))
  }

  // 分组聚合
  _groupBy(data, groupKey, valueKey) {
    const groups = data.reduce((acc, item) => {
      const key = this._safeGet(item, groupKey, 'unknown')
      acc[key] = (acc[key] || 0) + this._safeGet(item, valueKey, 0)
      return acc
    }, {})

    return Object.entries(groups).map(([name, value]) => ({ name, value }))
  }
} 