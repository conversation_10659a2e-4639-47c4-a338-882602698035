export function useChartConfig() {
  const commonStyle = {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#ffffff'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    }
  }

  const getChartConfig = (type, data) => {
    const baseConfig = {
      ...commonStyle,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00d4ff',
        textStyle: {
          color: '#ffffff'
        }
      }
    }

    switch (type) {
      case 'bar':
        return {
          ...baseConfig,
          xAxis: {
            type: 'category',
            data: data.categories,
            axisLine: { lineStyle: { color: '#00d4ff' } },
            axisLabel: { color: '#ffffff' }
          },
          yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#00d4ff' } },
            axisLabel: { color: '#ffffff' },
            splitLine: { lineStyle: { color: 'rgba(0, 212, 255, 0.2)' } }
          },
          series: data.series.map(item => ({
            ...item,
            type: 'bar',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: '#00d4ff' },
                  { offset: 1, color: '#0066cc' }
                ]
              }
            }
          }))
        }

      case 'horizontal-bar':
        return {
          ...baseConfig,
          xAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#00d4ff' } },
            axisLabel: { color: '#ffffff' },
            splitLine: { lineStyle: { color: 'rgba(0, 212, 255, 0.2)' } }
          },
          yAxis: {
            type: 'category',
            data: data.categories,
            axisLine: { lineStyle: { color: '#00d4ff' } },
            axisLabel: { color: '#ffffff' }
          },
          series: data.series.map(item => ({
            ...item,
            type: 'bar',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                  { offset: 0, color: '#00d4ff' },
                  { offset: 1, color: '#0066cc' }
                ]
              }
            }
          }))
        }

      case 'line':
        return {
          ...baseConfig,
          xAxis: {
            type: 'category',
            data: data.categories,
            axisLine: { lineStyle: { color: '#00d4ff' } },
            axisLabel: { color: '#ffffff' }
          },
          yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#00d4ff' } },
            axisLabel: { color: '#ffffff' },
            splitLine: { lineStyle: { color: 'rgba(0, 212, 255, 0.2)' } }
          },
          series: data.series.map(item => ({
            ...item,
            type: 'line',
            smooth: true,
            lineStyle: { color: '#00d4ff', width: 3 },
            itemStyle: { color: '#00d4ff' },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                  { offset: 1, color: 'rgba(0, 212, 255, 0.1)' }
                ]
              }
            }
          }))
        }

      case 'pie':
        return {
          ...commonStyle,
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff',
            textStyle: { color: '#ffffff' }
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: { color: '#ffffff' }
          },
          series: [{
            type: 'pie',
            radius: '50%',
            data: data.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 212, 255, 0.5)'
              }
            },
            itemStyle: {
              color: function(params) {
                const colors = ['#00d4ff', '#0099cc', '#006699', '#003366']
                return colors[params.dataIndex % colors.length]
              }
            }
          }]
        }

      default:
        return baseConfig
    }
  }

  return {
    getChartConfig
  }
}
