import { ref } from 'vue'

export function useChartConfig() {
  // 基础配置
  const baseConfig = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      right: '5%',
      bottom: '10%',
      left: '10%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: 'rgba(255,255,255,0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      textStyle: {
        color: '#fff'
      },
      top: 0
    }
  }

  // 饼图配置
  const pieConfig = {
    ...baseConfig,
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderColor: '#1a1a1a',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{d}%',
        color: '#fff'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      data: [
        { value: 1048, name: '类型A' },
        { value: 735, name: '类型B' },
        { value: 580, name: '类型C' },
        { value: 484, name: '类型D' },
        { value: 300, name: '类型E' }
      ]
    }]
  }

  // 柱状图配置
  const barConfig = {
    ...baseConfig,
    xAxis: {
      type: 'category',
      data: ['企业A', '企业B', '企业C', '企业D', '企业E'],
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        color: '#fff',
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0.1)'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [{
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: '#83bff6'
        }, {
          offset: 0.5,
          color: '#188df0'
        }, {
          offset: 1,
          color: '#188df0'
        }])
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: '#2378f7'
          }, {
            offset: 0.7,
            color: '#2378f7'
          }, {
            offset: 1,
            color: '#83bff6'
          }])
        }
      },
      data: [220, 182, 191, 234, 290]
    }]
  }

  // 折线图配置
  const lineConfig = {
    ...baseConfig,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0.1)'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [{
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#0ff'
      },
      lineStyle: {
        color: '#0ff',
        width: 2
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(0,255,255,0.3)'
        }, {
          offset: 1,
          color: 'rgba(0,255,255,0)'
        }])
      },
      data: [150, 230, 224, 218, 135, 147]
    }]
  }

  // 获取配置
  const getConfig = (type) => {
    switch (type) {
      case 'pie':
        return pieConfig
      case 'bar':
        return barConfig
      case 'line':
        return lineConfig
      default:
        return baseConfig
    }
  }

  return {
    getConfig
  }
}