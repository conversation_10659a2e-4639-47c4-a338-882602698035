/**
 * 海关业务HTTP请求工具
 */

import { API_CONFIG } from '../constants/index.js'

// 请求缓存
const requestCache = new Map()

// 生成缓存键
const generateCacheKey = (config) => {
  return `${config.method}_${config.url}_${JSON.stringify(config.params || {})}`
}

// 检查缓存
const getFromCache = (key) => {
  const cached = requestCache.get(key)
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data
  }
  requestCache.delete(key)
  return null
}

// 设置缓存
const setCache = (key, data, ttl) => {
  requestCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  })
}

// 核心请求函数
const makeRequest = async (config) => {
  const {
    url,
    method = 'GET',
    params,
    data,
    headers = {},
    timeout = API_CONFIG.TIMEOUT
  } = config

  // 构建完整URL
  const baseURL = API_CONFIG.BASE_URL
  let fullUrl = `${baseURL}${url}`

  // 处理GET请求参数
  if (method === 'GET' && params) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    const queryString = searchParams.toString()
    if (queryString) {
      fullUrl += `?${queryString}`
    }
  }

  // 设置请求头
  const requestHeaders = {
    'Content-Type': 'application/json',
    ...headers
  }

  // 获取用户token
  const token = localStorage.getItem('customs_user_token')
  if (token) {
    requestHeaders.Authorization = `Bearer ${token}`
  }

  // 构建fetch选项
  const fetchOptions = {
    method,
    headers: requestHeaders
  }

  // 设置超时
  if (timeout) {
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    fetchOptions.signal = controller.signal
  }

  // 处理请求体
  if (method !== 'GET' && data) {
    fetchOptions.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(fullUrl, fetchOptions)
    
    // 检查响应状态
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}`)
    }

    const result = await response.json()
    return {
      code: 200,
      message: 'success',
      data: result,
      success: true,
      timestamp: Date.now()
    }
  } catch (error) {
    console.error('Request failed:', error)
    throw error
  }
}

// 请求重试
const retryRequest = async (config, retryCount = 0) => {
  try {
    return await makeRequest(config)
  } catch (error) {
    if (retryCount < (config.retry || API_CONFIG.RETRY_TIMES)) {
      await new Promise(resolve => setTimeout(resolve, API_CONFIG.RETRY_DELAY * (retryCount + 1)))
      return retryRequest(config, retryCount + 1)
    }
    throw error
  }
}

// 主要的请求函数
export const request = async (config) => {
  try {
    // 检查缓存
    if (config.cache && config.method === 'GET') {
      const cacheKey = generateCacheKey(config)
      const cached = getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    // 发送请求
    const response = await (config.retry ? retryRequest(config) : makeRequest(config))

    // 设置缓存
    if (config.cache && config.method === 'GET' && response.success) {
      const cacheKey = generateCacheKey(config)
      setCache(cacheKey, response, config.cacheTTL || 300000) // 默认5分钟
    }

    return response
  } catch (error) {
    return {
      code: 500,
      message: error instanceof Error ? error.message : '请求失败',
      data: null,
      success: false,
      timestamp: Date.now()
    }
  }
}

// GET请求
export const get = (url, params, options = {}) => {
  return request({
    url,
    method: 'GET',
    params,
    ...options
  })
}

// POST请求
export const post = (url, data, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
export const put = (url, data, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
export const del = (url, params, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    params,
    ...options
  })
}

// 文件上传
export const upload = async (url, file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)

  const xhr = new XMLHttpRequest()
  
  return new Promise((resolve, reject) => {
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = (event.loaded / event.total) * 100
        onProgress(progress)
      }
    })

    xhr.addEventListener('load', () => {
      try {
        const response = JSON.parse(xhr.responseText)
        resolve(response)
      } catch (error) {
        reject(new Error('响应解析失败'))
      }
    })

    xhr.addEventListener('error', () => {
      reject(new Error('上传失败'))
    })

    xhr.open('POST', `${API_CONFIG.BASE_URL}${url}`)
    
    const token = localStorage.getItem('customs_user_token')
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }
    
    xhr.send(formData)
  })
}

// 清除缓存
export const clearCache = (pattern) => {
  if (pattern) {
    for (const key of requestCache.keys()) {
      if (key.includes(pattern)) {
        requestCache.delete(key)
      }
    }
  } else {
    requestCache.clear()
  }
}

// 海关业务专用API
export const customsAPI = {
  // 获取加工增值数据
  getProcessingData: () => get('/customs/processing'),
  
  // 获取交通运输数据
  getTransportData: () => get('/customs/transport'),
  
  // 获取企业信息
  getEnterpriseInfo: (id) => get(`/customs/enterprise/${id}`),
  
  // 获取统计数据
  getStatistics: (type, params) => get(`/customs/statistics/${type}`, params),
  
  // 获取实时数据
  getRealTimeData: () => get('/customs/realtime', {}, { cache: false })
}
