<template>
  <div class="base-card relative w-full h-full rounded shadow-sm overflow-hidden" :class="{ loading, error }">
    <!-- 加载状态 -->
    <div v-if="loading" class="card-overlay flex items-center justify-center">
      <div class="flex flex-col items-center gap-2">
        <div class="loading-spinner"></div>
        <span class="text-sm text-secondary">加载中...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="card-overlay flex items-center justify-center">
      <div class="flex flex-col items-center gap-4">
        <div class="error-icon text-error w-12 h-12">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <span class="text-sm text-secondary text-center max-w-4/5">{{ error }}</span>
        <button 
          class="px-4 py-2 border border-error rounded text-sm text-error cursor-pointer transition hover:bg-error hover:text-white"
          @click="$emit('retry')"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div v-if="title" class="p-4 border-b">
      <h3 class="text-lg font-bold text-primary m-0">{{ title }}</h3>
    </div>
    <div class="p-4">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['retry'])
</script>

<style scoped>
.base-card {
  background-color: var(--nav-bg-color);
}

.card-overlay {
  position: absolute;
  inset: 0;
  background-color: var(--bg-color);
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>