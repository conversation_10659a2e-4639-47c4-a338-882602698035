<template>
  <div class="tab-container">
    <component 
      :is="currentComponent" 
      :loading="loading" 
      :data="data"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { componentMap } from './index';

// 属性定义
const props = defineProps({
  // 当前激活的标签页配置
  tabConfig: {
    type: Object,
    required: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 所有数据对象
  allData: {
    type: Object,
    default: () => ({})
  }
});

// 计算当前应该显示的组件
const currentComponent = computed(() => {
  return componentMap[props.tabConfig.component] || null;
});

// 根据tabConfig中的dataKey获取对应的数据
const data = computed(() => {
  return props.allData[props.tabConfig.dataKey] || {};
});
</script>

<style scoped>
.tab-container {
  height: 100%;
  width: 100%;
}
</style>