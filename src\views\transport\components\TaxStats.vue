<template>
  <div class="tax-stats">
    <!-- 数据卡片 -->
    <el-row :gutter="16" class="data-cards">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>零关税总额</span>
              <el-tag size="small">月度</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="value">{{ loading ? '--' : '¥3.45亿' }}</div>
            <div class="trend">
              <span class="label">同比</span>
              <span class="percent up">+15.3%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>免税总额</span>
              <el-tag size="small" type="success">月度</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="value">{{ loading ? '--' : '¥2.18亿' }}</div>
            <div class="trend">
              <span class="label">同比</span>
              <span class="percent up">+10.5%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>维修减免</span>
              <el-tag size="small" type="warning">月度</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="value">{{ loading ? '--' : '¥0.92亿' }}</div>
            <div class="trend">
              <span class="label">同比</span>
              <span class="percent down">-2.1%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="16">
        <!-- 企业排名 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">减免税费企业排名</div>
            <div class="chart-content">
              <v-chart :option="companyRankOption" autoresize />
            </div>
          </div>
        </el-col>
        <!-- 月度趋势 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">减免税费趋势分析</div>
            <div class="chart-content">
              <v-chart :option="taxTrendOption" autoresize />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import VChart from 'vue-echarts'

// 接收属性
const props = defineProps({
  loading: Boolean,
  data: Object
})

// 企业排名图表配置
const companyRankOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    name: '减免税费（万元）',
    axisLine: {
      lineStyle: {
        color: 'var(--text-color-secondary)'
      }
    },
    axisLabel: {
      color: 'var(--text-color)'
    }
  },
  yAxis: {
    type: 'category',
    data: ['企业A', '企业B', '企业C', '企业D', '企业E', '企业F', '企业G', '企业H', '企业I', '企业J'],
    axisLine: {
      lineStyle: {
        color: 'var(--text-color-secondary)'
      }
    },
    axisLabel: {
      color: 'var(--text-color)'
    }
  },
  series: [
    {
      name: '减免税费',
      type: 'bar',
      data: [1200, 980, 860, 750, 680, 560, 480, 420, 380, 320],
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [{
            offset: 0,
            color: 'var(--primary-color)'
          }, {
            offset: 1,
            color: 'var(--success-color)'
          }]
        }
      }
    }
  ]
}))

// 税费趋势图表配置
const taxTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['零关税', '免税', '维修减免'],
    textStyle: {
      color: 'var(--text-color)'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisLine: {
      lineStyle: {
        color: 'var(--text-color-secondary)'
      }
    },
    axisLabel: {
      color: 'var(--text-color)'
    }
  },
  yAxis: {
    type: 'value',
    name: '金额（万元）',
    axisLine: {
      lineStyle: {
        color: 'var(--text-color-secondary)'
      }
    },
    axisLabel: {
      color: 'var(--text-color)'
    }
  },
  series: [
    {
      name: '零关税',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [3200, 3020, 3410, 3740, 3900, 4500]
    },
    {
      name: '免税',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [2200, 1820, 1910, 2340, 2900, 3300]
    },
    {
      name: '维修减免',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [1500, 1320, 2010, 1540, 1900, 2300]
    }
  ]
}))
</script>

<style scoped>
.tax-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.data-cards {
  margin-bottom: var(--content-gap);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  text-align: center;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8px;
}

.trend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.label {
  color: var(--text-color-secondary);
}

.percent {
  font-weight: 500;
}

.percent.up {
  color: var(--success-color);
}

.percent.down {
  color: var(--danger-color);
}

.charts-container {
  flex: 1;
  min-height: 0;
}

.chart-wrapper {
  height: 100%;
  padding: var(--content-padding);
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;
}

.chart-content {
  height: calc(100% - 32px);
}
</style>