/**
 * 业务相关类型定义
 */

// 基础实体类型
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// 企业信息类型
export interface Enterprise extends BaseEntity {
  name: string
  code: string
  type: string
  region: string
  status: 'active' | 'inactive'
  registeredCapital: number
  businessScope: string[]
  contactInfo?: {
    phone: string
    email: string
    address: string
  }
}

// 货物信息类型
export interface Goods extends BaseEntity {
  name: string
  code: string
  category: string
  unit: string
  value: number
  quantity: number
  taxRate: number
  origin: string
  destination: string
}

// 加工增值业务类型
export interface ProcessingBusiness {
  // 货物总值及货量
  goodsStats: {
    totalValue: number
    totalQuantity: number
    unit: string
    period: string
    categories: Record<string, number>
  }
  
  // 免征税款
  taxExemption: {
    amount: number
    period: string
    enterprises: number
    byCategory: Record<string, number>
  }
  
  // 备案企业
  registeredEnterprises: {
    total: number
    active: number
    inactive: number
    byType: Record<string, number>
    byRegion: Record<string, number>
  }
  
  // 企业排行
  enterpriseRanking: {
    byValue: RankingItem[]
    byQuantity: RankingItem[]
    byTaxExemption: RankingItem[]
  }
  
  // 产值申报
  productionDeclaration: {
    count: number
    totalValue: number
    period: string
    byMonth: TimeSeriesData[]
  }
  
  // 发货申请
  shipmentApplication: {
    count: number
    totalValue: number
    destinations: Record<string, number>
  }
}

// 交通运输工具业务类型
export interface TransportBusiness {
  // 零关税进口小汽车
  zeroTariffCars: {
    count: number
    totalValue: number
    brands: Record<string, number>
    models: Record<string, number>
    monthlyTrend: TimeSeriesData[]
  }
  
  // 免税交通运输工具
  dutyFreeVehicles: {
    count: number
    categories: Record<string, number>
    valueByCategory: Record<string, number>
  }
  
  // 零配件维修
  spareParts: {
    repairCount: number
    partTypes: Record<string, number>
    totalCost: number
    monthlyRepairs: TimeSeriesData[]
  }
  
  // 综合分析
  comprehensiveAnalysis: {
    totalSavings: number
    beneficiaryCount: number
    popularCategories: RankingItem[]
  }
}

// 排行榜数据类型
export interface RankingItem {
  rank: number
  name: string
  value: number
  change?: number
  percentage?: number
  unit?: string
  category?: string
}

// 时间序列数据类型
export interface TimeSeriesData {
  date: string
  value: number
  category?: string
  label?: string
}

// 地区分布数据类型
export interface RegionDistribution {
  region: string
  count: number
  percentage: number
  coordinates?: [number, number]
  value?: number
}

// 业务指标类型
export interface BusinessIndicator {
  id: string
  name: string
  value: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  changeRate: number
  description?: string
  target?: number
  status: 'normal' | 'warning' | 'danger'
}

// 统计数据类型
export interface StatItem {
  label: string
  value: number | string
  unit?: string
  trend?: 'up' | 'down' | 'stable'
  change?: number
  icon?: string
  color?: string
}

// 业务统计类型
export interface BusinessStats {
  totalValue: number
  totalQuantity: number
  taxExemption: number
  enterpriseCount: number
  period: string
  growth: {
    valueGrowth: number
    quantityGrowth: number
    enterpriseGrowth: number
  }
}
