<template>
  <div class="pc-dashboard">
    <!-- PC端头部 -->
    <header class="pc-header">
      <div class="header-left">
        <h1>{{ pageConfig.title }}</h1>
        <div class="subtitle">{{ pageConfig.subtitle }}</div>
      </div>
      <div class="header-right">
        <div class="device-badge">💻 PC端</div>
        <div class="time-display">{{ currentTime }}</div>
      </div>
    </header>

    <!-- PC端主要内容 -->
    <main class="pc-main">
      <!-- 动态模块渲染 -->
      <section
        v-for="module in pageConfig.modules"
        :key="module.id"
        class="pc-section"
      >
        <div class="section-header">
          <h2>{{ module.icon }} {{ module.description }}</h2>
          <div class="section-actions">
            <button class="refresh-btn" @click="refreshData">🔄 刷新</button>
          </div>
        </div>

        <!-- 统计概览 - 4列布局 -->
        <div class="stats-grid-pc">
          <StatCard
            v-for="stat in module.stats"
            :key="stat.id"
            :title="stat.title"
            :value="stat.getValue()"
            :unit="stat.unit"
            :icon="stat.icon"
          />
        </div>

        <!-- 图表网格 - 3列布局 -->
        <div class="charts-grid-pc">
          <ChartCard
            v-for="chart in module.charts"
            :key="chart.id"
            :title="chart.title"
            :data="chart.getData()"
            :chart-type="chart.type"
            :height="350"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import ChartCard from '../components/ChartCard.vue'
import StatCard from '../components/StatCard.vue'
import { useDashboardContent } from '../composables/useDashboardContent'

export default {
  name: 'PCDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const currentTime = ref('')
    const { pageConfig, loadAllData } = useDashboardContent()

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const refreshData = async () => {
      await loadAllData()
    }

    let timeInterval = null

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
      loadAllData()
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      pageConfig,
      refreshData
    }
  }
}
</script>

<style scoped>
.pc-dashboard {
  width: 100%;
  min-height: 100vh;
  background:
    radial-gradient(ellipse at top, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(0, 100, 200, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
  color: #fff;
  position: relative;
  overflow-x: hidden;
}

.pc-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 98px,
      rgba(0, 212, 255, 0.03) 100px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 98px,
      rgba(0, 212, 255, 0.03) 100px
    );
  pointer-events: none;
  z-index: 1;
}

.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 40px;
  background:
    linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 50, 100, 0.3) 100%),
    rgba(0, 0, 0, 0.4);
  border-bottom: 3px solid #00d4ff;
  box-shadow:
    0 4px 20px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 10;
}

.pc-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
}

.pc-header::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff, #0099cc, #00d4ff);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

.header-left h1 {
  font-size: 2.8rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00d4ff 0%, #ffffff 50%, #00d4ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow:
    0 0 20px rgba(0, 212, 255, 0.8),
    0 0 40px rgba(0, 212, 255, 0.4);
  margin-bottom: 8px;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.device-badge {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 100, 200, 0.2) 100%);
  border: 2px solid #00d4ff;
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: bold;
  box-shadow:
    0 0 15px rgba(0, 212, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.8);
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 1.3rem;
  font-weight: bold;
  color: #00d4ff;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.5);
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.pc-main {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.pc-section {
  background:
    linear-gradient(135deg, rgba(0, 212, 255, 0.08) 0%, rgba(0, 100, 200, 0.05) 100%),
    rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 30px;
  border: 2px solid rgba(0, 212, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 20px rgba(0, 212, 255, 0.1);
  position: relative;
  backdrop-filter: blur(10px);
  z-index: 5;
}

.pc-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  background: linear-gradient(135deg, transparent, rgba(0, 212, 255, 0.05), transparent);
  pointer-events: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.section-header h2 {
  font-size: 2.2rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00d4ff 0%, #ffffff 50%, #00d4ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow:
    0 0 15px rgba(0, 212, 255, 0.8),
    0 0 30px rgba(0, 212, 255, 0.4);
  letter-spacing: 1px;
  position: relative;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff, transparent);
  border-radius: 2px;
}

.refresh-btn {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 100, 200, 0.2) 100%);
  border: 2px solid #00d4ff;
  border-radius: 12px;
  color: #00d4ff;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 15px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.8);
}

.refresh-btn:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.5) 0%, rgba(0, 100, 200, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 212, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.refresh-btn:active {
  transform: translateY(0);
}

.stats-grid-pc {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.charts-grid-pc {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
</style>
