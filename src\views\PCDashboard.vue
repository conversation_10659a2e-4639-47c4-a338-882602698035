<template>
  <div class="pc-dashboard">
    <!-- PC端头部 -->
    <header class="pc-header">
      <div class="header-left">
        <h1>{{ pageConfig.title }}</h1>
        <div class="subtitle">{{ pageConfig.subtitle }}</div>
      </div>
      <div class="header-right">
        <div class="device-badge">💻 PC端</div>
        <div class="time-display">{{ currentTime }}</div>
      </div>
    </header>

    <!-- PC端主要内容 -->
    <main class="pc-main">
      <!-- 动态模块渲染 -->
      <section
        v-for="module in pageConfig.modules"
        :key="module.id"
        class="pc-section"
      >
        <div class="section-header">
          <h2>{{ module.icon }} {{ module.description }}</h2>
          <div class="section-actions">
            <button class="refresh-btn" @click="refreshData">🔄 刷新</button>
          </div>
        </div>

        <!-- 统计概览 - 4列布局 -->
        <div class="stats-grid-pc">
          <StatCard
            v-for="stat in module.stats"
            :key="stat.id"
            :title="stat.title"
            :value="stat.getValue()"
            :unit="stat.unit"
            :icon="stat.icon"
          />
        </div>

        <!-- 图表网格 - 3列布局 -->
        <div class="charts-grid-pc">
          <ChartCard
            v-for="chart in module.charts"
            :key="chart.id"
            :title="chart.title"
            :data="chart.getData()"
            :chart-type="chart.type"
            :height="350"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import ChartCard from '../components/ChartCard.vue'
import StatCard from '../components/StatCard.vue'
import { useDashboardContent } from '../composables/useDashboardContent'

export default {
  name: 'PCDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const currentTime = ref('')
    const { pageConfig, loadAllData } = useDashboardContent()

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const refreshData = async () => {
      await loadAllData()
    }

    let timeInterval = null

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
      loadAllData()
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      pageConfig,
      refreshData
    }
  }
}
</script>

<style scoped>
.pc-dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  color: #fff;
}

.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid #00d4ff;
}

.header-left h1 {
  font-size: 2.2rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  margin-bottom: 5px;
}

.subtitle {
  font-size: 0.9rem;
  opacity: 0.7;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.device-badge {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.9rem;
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 1rem;
  color: #00d4ff;
}

.pc-main {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.pc-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.section-header h2 {
  font-size: 1.8rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.refresh-btn {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  color: #00d4ff;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
}

.stats-grid-pc {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.charts-grid-pc {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
</style>
