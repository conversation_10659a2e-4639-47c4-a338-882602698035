<template>
  <div class="pc-dashboard">
    <!-- PC端头部 - 按示例图设计 -->
    <header class="pc-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo">🏛️</div>
          <div class="title-section">
            <h1>中国（海南）国际贸易单一窗口领导视窗</h1>
            <div class="subtitle">加工增值业务统计</div>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="nav-menu">
          <button class="nav-item active">加工增值</button>
          <button class="nav-item">交通运输工具</button>
          <button class="nav-item">其他业务</button>
        </div>
        <div class="time-display">{{ currentTime }}</div>
      </div>
    </header>

    <!-- PC端主要内容 - 按示例图重新设计 -->
    <main class="pc-main">
      <!-- 顶部统计卡片区域 -->
      <section class="top-stats-section">
        <div class="stats-grid-top">
          <div class="stat-card">
            <div class="stat-title">内地货物（万元）</div>
            <div class="stat-value">3,456</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">内地货物（吨）</div>
            <div class="stat-value">1,456</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">外地货物（万元）</div>
            <div class="stat-value">456</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">外地货物（吨）</div>
            <div class="stat-value">5,456</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">重点企业（家）</div>
            <div class="stat-value">3,456</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">进口金额（万元）</div>
            <div class="stat-value">1,456</div>
          </div>
        </div>
      </section>

      <!-- 主要图表区域 -->
      <section class="main-charts-section">
        <div class="charts-layout">
          <!-- 左侧图表列 -->
          <div class="charts-column left">
            <div class="chart-item">
              <ChartCard
                title="重点产业分布"
                :data="getIndustryDistributionData()"
                chart-type="pie"
                :height="250"
              />
            </div>
            <div class="chart-item">
              <ChartCard
                title="加工增值内销分析"
                :data="getProcessingAnalysisData()"
                chart-type="bar"
                :height="250"
              />
            </div>
          </div>

          <!-- 中间图表列 -->
          <div class="charts-column center">
            <div class="chart-item">
              <ChartCard
                title="企业主要地区分布"
                :data="getRegionDistributionData()"
                chart-type="horizontal-bar"
                :height="250"
              />
            </div>
            <div class="chart-item">
              <ChartCard
                title="加工增值产值TOP5（万元）"
                :data="getTop5ProductionData()"
                chart-type="pie"
                :height="250"
              />
            </div>
          </div>

          <!-- 右侧数据表格区域 -->
          <div class="data-table-section">
            <div class="table-header">
              <h3>内地主要地区统计</h3>
            </div>
            <div class="data-table">
              <table>
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>内地关区</th>
                    <th>Ahui（万元）</th>
                    <th>占比例</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in getTableData()" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.region }}</td>
                    <td>{{ item.amount }}</td>
                    <td>{{ item.percentage }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import ChartCard from '../components/ChartCard.vue'
import StatCard from '../components/StatCard.vue'
import { useDashboardContent } from '../composables/useDashboardContent'

export default {
  name: 'PCDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const currentTime = ref('')
    const { pageConfig, loadAllData } = useDashboardContent()

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const refreshData = async () => {
      await loadAllData()
    }

    // 新增数据方法
    const getIndustryDistributionData = () => ({
      series: [
        { name: '电子信息', value: 335 },
        { name: '汽车制造', value: 310 },
        { name: '生物医药', value: 234 },
        { name: '新材料', value: 135 },
        { name: '其他', value: 86 }
      ]
    })

    const getProcessingAnalysisData = () => ({
      categories: ['海口', '三亚', '儋州', '琼海', '文昌'],
      series: [{
        name: '产值',
        data: [120, 200, 150, 80, 70]
      }]
    })

    const getRegionDistributionData = () => ({
      categories: ['广东', '浙江', '江苏', '上海', '福建'],
      series: [{
        name: '企业数',
        data: [143, 142, 141, 84, 22]
      }]
    })

    const getTop5ProductionData = () => ({
      series: [
        { name: '广东', value: 64.91 },
        { name: '浙江', value: 62.12 },
        { name: '江苏', value: 48.91 },
        { name: '上海', value: 22.91 },
        { name: '福建', value: 19.02 }
      ]
    })

    const getTableData = () => [
      { region: '广东', amount: '64.91', percentage: '143' },
      { region: '浙江', amount: '62.12', percentage: '80' },
      { region: '江苏', amount: '48.91', percentage: '60' },
      { region: '上海', amount: '22.91', percentage: '31' },
      { region: '福建', amount: '19.02', percentage: '18' }
    ]

    let timeInterval = null

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
      loadAllData()
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      pageConfig,
      refreshData,
      getIndustryDistributionData,
      getProcessingAnalysisData,
      getRegionDistributionData,
      getTop5ProductionData,
      getTableData
    }
  }
}
</script>

<style scoped>
.pc-dashboard {
  width: 100%;
  min-height: 100vh;
  background:
    radial-gradient(ellipse at top, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(0, 100, 200, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
  color: #fff;
  position: relative;
  overflow-x: hidden;
}

.pc-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 98px,
      rgba(0, 212, 255, 0.03) 100px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 98px,
      rgba(0, 212, 255, 0.03) 100px
    );
  pointer-events: none;
  z-index: 1;
}

.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.6);
  border-bottom: 2px solid #00d4ff;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  font-size: 2rem;
}

.title-section h1 {
  font-size: 1.8rem;
  color: #00d4ff;
  margin: 0 0 5px 0;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-menu {
  display: flex;
  gap: 10px;
}

.nav-item {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: rgba(0, 212, 255, 0.1);
  color: #fff;
}

.nav-item.active {
  background: rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  border-color: #00d4ff;
}

.time-display {
  font-size: 0.9rem;
  color: #00d4ff;
  font-family: 'Courier New', monospace;
}

.pc-main {
  padding: 20px;
  position: relative;
  z-index: 2;
}

/* 顶部统计区域 */
.top-stats-section {
  margin-bottom: 20px;
}

.stats-grid-top {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 15px;
}

.stat-card {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #00d4ff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.stat-title {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00d4ff;
}

/* 主要图表区域 */
.main-charts-section {
  height: calc(100vh - 200px);
}

.charts-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  height: 100%;
}

.charts-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 8px;
}

/* 数据表格区域 */
.data-table-section {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.table-header h3 {
  color: #00d4ff;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.data-table {
  max-height: 400px;
  overflow-y: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.data-table th {
  background: rgba(0, 212, 255, 0.1);
  color: #00d4ff;
  font-weight: bold;
  font-size: 0.9rem;
}

.data-table td {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

.data-table tr:hover {
  background: rgba(0, 212, 255, 0.05);
}

.pc-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
}

.pc-header::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff, #0099cc, #00d4ff);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

.header-left h1 {
  font-size: 2.8rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00d4ff 0%, #ffffff 50%, #00d4ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow:
    0 0 20px rgba(0, 212, 255, 0.8),
    0 0 40px rgba(0, 212, 255, 0.4);
  margin-bottom: 8px;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.device-badge {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 100, 200, 0.2) 100%);
  border: 2px solid #00d4ff;
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: bold;
  box-shadow:
    0 0 15px rgba(0, 212, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.8);
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 1.3rem;
  font-weight: bold;
  color: #00d4ff;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.5);
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.pc-main {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.pc-section {
  background:
    linear-gradient(135deg, rgba(0, 212, 255, 0.08) 0%, rgba(0, 100, 200, 0.05) 100%),
    rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 30px;
  border: 2px solid rgba(0, 212, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 20px rgba(0, 212, 255, 0.1);
  position: relative;
  backdrop-filter: blur(10px);
  z-index: 5;
}

.pc-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  background: linear-gradient(135deg, transparent, rgba(0, 212, 255, 0.05), transparent);
  pointer-events: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.section-header h2 {
  font-size: 2.2rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00d4ff 0%, #ffffff 50%, #00d4ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow:
    0 0 15px rgba(0, 212, 255, 0.8),
    0 0 30px rgba(0, 212, 255, 0.4);
  letter-spacing: 1px;
  position: relative;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff, transparent);
  border-radius: 2px;
}

.refresh-btn {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 100, 200, 0.2) 100%);
  border: 2px solid #00d4ff;
  border-radius: 12px;
  color: #00d4ff;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 15px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.8);
}

.refresh-btn:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.5) 0%, rgba(0, 100, 200, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 212, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.refresh-btn:active {
  transform: translateY(0);
}

.stats-grid-pc {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.charts-grid-pc {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
</style>
