/**
 * 图表配置工厂
 * 根据图表类型和数据生成ECharts配置
 */

// 默认颜色配置
const DEFAULT_COLORS = [
  '#1890ff', '#13c2c2', '#52c41a', '#faad14', 
  '#f5222d', '#722ed1', '#eb2f96', '#fa541c'
]

// 深色主题配置
const DARK_THEME = {
  backgroundColor: 'transparent',
  textColor: '#ffffff',
  axisColor: '#4a5568',
  gridColor: '#2d3748',
  tooltipBg: 'rgba(0, 0, 0, 0.8)',
  tooltipBorder: '#1890ff'
}

/**
 * 创建图表配置
 * @param {string} type - 图表类型
 * @param {Array} data - 图表数据
 * @param {Object} config - 自定义配置
 * @returns {Object} ECharts配置对象
 */
export function createChartOption(type, data, config = {}) {
  const baseConfig = {
    backgroundColor: DARK_THEME.backgroundColor,
    color: config.colors || DEFAULT_COLORS,
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  switch (type) {
    case 'bar':
      return createBarChart(data, config, baseConfig)
    case 'line':
      return createLineChart(data, config, baseConfig)
    case 'pie':
      return createPieChart(data, config, baseConfig)
    case 'doughnut':
      return createDoughnutChart(data, config, baseConfig)
    case 'radar':
      return createRadarChart(data, config, baseConfig)
    case 'gauge':
      return createGaugeChart(data, config, baseConfig)
    default:
      console.warn(`不支持的图表类型: ${type}`)
      return baseConfig
  }
}

/**
 * 创建柱状图配置
 */
function createBarChart(data, config, baseConfig) {
  const isHorizontal = config.direction === 'horizontal'
  
  // 处理数据
  const processedData = processChartData(data)
  const categories = processedData.map(item => item.name)
  const values = processedData.map(item => item.value)

  return {
    ...baseConfig,
    title: createTitle(config.title),
    tooltip: createTooltip('axis'),
    grid: createGrid(config.grid),
    xAxis: {
      type: isHorizontal ? 'value' : 'category',
      data: isHorizontal ? undefined : categories,
      ...createAxis(config.xAxis)
    },
    yAxis: {
      type: isHorizontal ? 'category' : 'value',
      data: isHorizontal ? categories : undefined,
      ...createAxis(config.yAxis)
    },
    series: [{
      type: 'bar',
      data: values,
      barWidth: config.barWidth || 'auto',
      itemStyle: {
        borderRadius: isHorizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
      },
      label: config.showLabel ? {
        show: true,
        position: isHorizontal ? 'right' : 'top',
        color: DARK_THEME.textColor,
        fontSize: 12
      } : { show: false },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(24, 144, 255, 0.5)'
        }
      }
    }]
  }
}

/**
 * 创建折线图配置
 */
function createLineChart(data, config, baseConfig) {
  const processedData = processChartData(data)
  const categories = processedData.map(item => item.name)
  const values = processedData.map(item => item.value)

  return {
    ...baseConfig,
    title: createTitle(config.title),
    tooltip: createTooltip('axis'),
    grid: createGrid(config.grid),
    xAxis: {
      type: 'category',
      data: categories,
      ...createAxis(config.xAxis)
    },
    yAxis: {
      type: 'value',
      ...createAxis(config.yAxis)
    },
    series: [{
      type: 'line',
      data: values,
      smooth: config.smooth !== false,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3
      },
      areaStyle: config.showArea ? {
        opacity: 0.3
      } : undefined,
      label: config.showLabel ? {
        show: true,
        position: 'top',
        color: DARK_THEME.textColor,
        fontSize: 12
      } : { show: false }
    }]
  }
}

/**
 * 创建饼图配置
 */
function createPieChart(data, config, baseConfig) {
  const processedData = processChartData(data)

  return {
    ...baseConfig,
    title: createTitle(config.title),
    tooltip: createTooltip('item', '{a} <br/>{b}: {c} ({d}%)'),
    legend: config.showLegend !== false ? {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: DARK_THEME.textColor
      }
    } : { show: false },
    series: [{
      type: 'pie',
      radius: config.radius || '70%',
      center: config.center || ['50%', '50%'],
      data: processedData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: config.showLabel !== false ? {
        formatter: '{b}: {d}%',
        color: DARK_THEME.textColor
      } : { show: false }
    }]
  }
}

/**
 * 创建环形图配置
 */
function createDoughnutChart(data, config, baseConfig) {
  const pieConfig = createPieChart(data, config, baseConfig)
  pieConfig.series[0].radius = config.radius || ['40%', '70%']
  return pieConfig
}

/**
 * 创建雷达图配置
 */
function createRadarChart(data, config, baseConfig) {
  const indicators = config.indicators || []
  
  return {
    ...baseConfig,
    title: createTitle(config.title),
    tooltip: createTooltip('item'),
    radar: {
      indicator: indicators,
      axisLine: {
        lineStyle: {
          color: DARK_THEME.axisColor
        }
      },
      splitLine: {
        lineStyle: {
          color: DARK_THEME.gridColor
        }
      },
      axisLabel: {
        color: DARK_THEME.textColor
      }
    },
    series: [{
      type: 'radar',
      data: data.map(item => ({
        value: item.value,
        name: item.name
      }))
    }]
  }
}

/**
 * 创建仪表盘配置
 */
function createGaugeChart(data, config, baseConfig) {
  const value = Array.isArray(data) ? data[0]?.value || 0 : data

  return {
    ...baseConfig,
    title: createTitle(config.title),
    series: [{
      type: 'gauge',
      min: config.min || 0,
      max: config.max || 100,
      data: [{ value, name: config.name || '指标' }],
      axisLine: {
        lineStyle: {
          width: 30,
          color: [
            [0.3, '#67e0e3'],
            [0.7, '#37a2da'],
            [1, '#fd666d']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: 'auto'
        }
      },
      axisTick: {
        distance: -30,
        length: 8,
        lineStyle: {
          color: '#fff',
          width: 2
        }
      },
      splitLine: {
        distance: -30,
        length: 30,
        lineStyle: {
          color: '#fff',
          width: 4
        }
      },
      axisLabel: {
        color: 'auto',
        distance: 40,
        fontSize: 20
      },
      detail: {
        valueAnimation: true,
        formatter: '{value}%',
        color: 'auto'
      }
    }]
  }
}

/**
 * 处理图表数据
 */
function processChartData(data) {
  if (!Array.isArray(data)) return []
  
  return data.map(item => {
    if (typeof item === 'object' && item !== null) {
      return {
        name: item.name || item.label || item.key || '',
        value: item.value || item.count || item.amount || 0
      }
    }
    return {
      name: String(item),
      value: Number(item) || 0
    }
  })
}

/**
 * 创建标题配置
 */
function createTitle(title) {
  if (!title) return undefined
  
  return {
    text: title,
    left: 'center',
    top: 20,
    textStyle: {
      color: DARK_THEME.textColor,
      fontSize: 16,
      fontWeight: 'bold'
    }
  }
}

/**
 * 创建提示框配置
 */
function createTooltip(trigger, formatter) {
  return {
    trigger,
    formatter,
    backgroundColor: DARK_THEME.tooltipBg,
    borderColor: DARK_THEME.tooltipBorder,
    borderWidth: 1,
    textStyle: {
      color: DARK_THEME.textColor
    }
  }
}

/**
 * 创建网格配置
 */
function createGrid(gridConfig = {}) {
  return {
    left: '10%',
    right: '10%',
    top: '15%',
    bottom: '15%',
    containLabel: true,
    ...gridConfig
  }
}

/**
 * 创建坐标轴配置
 */
function createAxis(axisConfig = {}) {
  return {
    axisLine: {
      lineStyle: {
        color: DARK_THEME.axisColor
      }
    },
    axisTick: {
      lineStyle: {
        color: DARK_THEME.axisColor
      }
    },
    axisLabel: {
      color: DARK_THEME.textColor,
      fontSize: 12
    },
    splitLine: {
      lineStyle: {
        color: DARK_THEME.gridColor,
        type: 'dashed'
      }
    },
    ...axisConfig
  }
}
