import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
    {
      path: '/',
    redirect: '/processing'
    },
    {
      path: '/processing',
      name: 'Processing',
    component: () => import('@/views/ProcessingView.vue')
    },
    {
      path: '/transport',
      name: 'Transport',
    component: () => import('@/views/TransportView.vue')
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '海南海关领导视窗'}`;
  next();
});

export default router;