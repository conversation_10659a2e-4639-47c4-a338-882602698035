import { createRouter, createWebHistory } from 'vue-router'
import AutoRedirect from '../views/AutoRedirect.vue'
import MobileDashboard from '../views/MobileDashboard.vue'
import PCDashboard from '../views/PCDashboard.vue'
import WallDashboard from '../views/WallDashboard.vue'

const routes = [
  // 自动跳转方案 - 根据设备检测自动跳转
  {
    path: '/',
    name: 'AutoRedirect',
    component: AutoRedirect
  },

  // 手动跳转方案 - 直接URL访问
  {
    path: '/mobile',
    name: 'Mobile',
    component: MobileDashboard,
    meta: { deviceType: 'mobile' }
  },
  {
    path: '/pc',
    name: 'PC',
    component: PCDashboard,
    meta: { deviceType: 'pc' }
  },
  {
    path: '/wall',
    name: 'Wall',
    component: WallDashboard,
    meta: { deviceType: 'wall' }
  },

  // 兼容性路由
  {
    path: '/dashboard',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
