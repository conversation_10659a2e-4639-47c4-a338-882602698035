import { createRouter, createWebHistory } from 'vue-router'
import MobileDashboard from '../views/MobileDashboard.vue'
import PCDashboard from '../views/PCDashboard.vue'
import WallDashboard from '../views/WallDashboard.vue'

const routes = [
  // 默认跳转到PC端
  {
    path: '/',
    redirect: '/pc'
  },

  // 三种设备类型的直接URL访问
  {
    path: '/mobile',
    name: 'Mobile',
    component: MobileDashboard,
    meta: { deviceType: 'mobile' }
  },
  {
    path: '/pc',
    name: 'PC',
    component: PCDashboard,
    meta: { deviceType: 'pc' }
  },
  {
    path: '/wall',
    name: 'Wall',
    component: WallDashboard,
    meta: { deviceType: 'wall' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
