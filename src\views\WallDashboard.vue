<template>
  <div class="wall-dashboard">
    <!-- 大屏头部 -->
    <header class="wall-header">
      <div class="header-decoration left"></div>
      <div class="header-center">
        <h1>{{ pageConfig.title }}</h1>
        <div class="subtitle">{{ pageConfig.subtitle }}</div>
      </div>
      <div class="header-decoration right">
        <div class="device-badge">🖥️ 大屏墙面</div>
        <div class="time-display">{{ currentTime }}</div>
      </div>
    </header>

    <!-- 大屏主要内容 -->
    <main class="wall-main">
      <!-- 动态模块渲染 -->
      <section
        v-for="module in pageConfig.modules"
        :key="module.id"
        class="wall-section"
      >
        <div class="section-title">
          <div class="title-icon">{{ module.icon }}</div>
          <h2>{{ module.description }}</h2>
          <div class="title-line"></div>
        </div>

        <!-- 核心统计 - 6列布局 -->
        <div class="stats-grid-wall">
          <StatCard
            v-for="stat in module.stats"
            :key="stat.id"
            :title="stat.title"
            :value="stat.getValue()"
            :unit="stat.unit"
            :icon="stat.icon"
          />
        </div>

        <!-- 图表网格 - 4列布局 -->
        <div class="charts-grid-wall">
          <ChartCard
            v-for="chart in module.charts"
            :key="chart.id"
            :title="chart.title"
            :data="chart.getData()"
            :chart-type="chart.type"
            :height="400"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import ChartCard from '../components/ChartCard.vue'
import StatCard from '../components/StatCard.vue'
import { useDashboardContent } from '../composables/useDashboardContent'

export default {
  name: 'WallDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const currentTime = ref('')
    const { pageConfig, loadAllData } = useDashboardContent()

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    let timeInterval = null

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
      loadAllData()
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      pageConfig
    }
  }
}
</script>

<style scoped>
.wall-dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  color: #fff;
  overflow-x: auto;
}

.wall-header {
  display: flex;
  align-items: center;
  padding: 25px 40px;
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 3px solid #00d4ff;
  position: relative;
}

.header-decoration {
  width: 200px;
  height: 60px;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 30px;
}

.header-center {
  flex: 1;
  text-align: center;
  margin: 0 40px;
}

.header-center h1 {
  font-size: 3.5rem;
  color: #00d4ff;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  margin-bottom: 10px;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  letter-spacing: 2px;
}

.header-decoration.right {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.device-badge {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 1.1rem;
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 1.2rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.wall-main {
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 50px;
}

.wall-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 35px;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 35px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(0, 212, 255, 0.3);
}

.title-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.section-title h2 {
  font-size: 2.5rem;
  color: #00d4ff;
  text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
  margin-right: 30px;
}

.title-line {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, transparent);
}

.stats-grid-wall {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 25px;
  margin-bottom: 40px;
}

.charts-grid-wall {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
}
</style>
