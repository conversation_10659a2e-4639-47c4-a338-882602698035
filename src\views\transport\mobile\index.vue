<template>
  <transport-base
    class="p-4 gap-4"
    charts-layout="grid-1"
    tables-layout="grid-1"
  />
</template>

<script setup>
import TransportBase from '../components/TransportBase.vue'
</script>

<style scoped>
:deep(.stats-section) {
  grid-template-columns: repeat(2, 1fr);
}

:deep(.chart-wrapper),
:deep(.table-wrapper) {
  padding: 0.75rem;
}

:deep(.chart-title),
:deep(.table-title) {
  font-size: 1rem;
  margin-bottom: 0.75rem;
}

:deep(.data-table th),
:deep(.data-table td) {
  font-size: 0.875rem;
  padding: 0.75rem;
}
</style>