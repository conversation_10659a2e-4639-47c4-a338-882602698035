<template>
  <div class="transport-mobile-view">
    <TransportChart :data="chartData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useTransportData } from '../../../middleware/transport'
import TransportChart from '../../../components/TransportChart.vue'

const chartData = ref({})
onMounted(async () => {
  chartData.value = await useTransportData()
})
</script>

<style scoped>
.transport-mobile-view { width: 100vw; height: 100vh; background: #101c2a; }
</style> 