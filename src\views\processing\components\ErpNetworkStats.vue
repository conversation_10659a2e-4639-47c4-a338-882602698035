<template>
  <div class="erp-network-stats" :class="theme">
    <!-- 数据卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>ERP联网企业数量</span>
            <el-tag type="success" effect="dark">实时统计</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ formatNumber(data.totalCompanies) }} 家</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'bar',
                  data: data.monthlyTrend.map(item => item.count),
                  barWidth: '30%',
                  itemStyle: {
                    borderRadius: [4, 4, 0, 0]
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>区内企业</span>
            <el-tag type="primary" effect="dark">{{ formatNumber(data.insideCompanies) }} 家</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ ((data.insideCompanies / data.totalCompanies) * 100).toFixed(1) }}%</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'line',
                  data: data.monthlyTrend.map(item => item.inside),
                  smooth: true,
                  showSymbol: false,
                  lineStyle: { width: 3 },
                  areaStyle: {
                    opacity: 0.1
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>区外企业</span>
            <el-tag type="warning" effect="dark">{{ formatNumber(data.outsideCompanies) }} 家</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ ((data.outsideCompanies / data.totalCompanies) * 100).toFixed(1) }}%</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'line',
                  data: data.monthlyTrend.map(item => item.outside),
                  smooth: true,
                  showSymbol: false,
                  lineStyle: { width: 3 },
                  areaStyle: {
                    opacity: 0.1
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>ERP联网数量TOP5关区</span>
            <el-tag type="info" effect="dark">实时统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="topRegionsChartOption"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>企业分布趋势</span>
            <el-tag type="primary" effect="dark">近6月统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="distributionTrendChartOption"
            autoresize
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores'
import { formatNumber } from '@/utils/format'

// 属性定义
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({
      totalCompanies: 0,
      insideCompanies: 0,
      outsideCompanies: 0,
      monthlyTrend: [],
      topRegions: [],
      topCompanies: []
    })
  }
})

// 主题状态
const themeStore = useThemeStore()
const theme = computed(() => themeStore.theme)

// 月份数据
const monthData = ['1月', '2月', '3月', '4月', '5月', '6月']

// TOP5关区图表配置
const topRegionsChartOption = computed(() => ({
  tooltip: { 
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLabel: {
      formatter: value => formatNumber(value)
    }
  },
  yAxis: {
    type: 'category',
    data: props.data.topRegions.map(item => item.name),
    axisLabel: {
      interval: 0
    }
  },
  series: [{
    name: '企业数量',
    type: 'bar',
    data: props.data.topRegions.map(item => item.value),
    barWidth: '60%',
    itemStyle: {
      borderRadius: [0, 4, 4, 0]
    },
    label: {
      show: true,
      position: 'right',
      formatter: value => formatNumber(value)
    }
  }]
}))

// 企业分布趋势图表配置
const distributionTrendChartOption = computed(() => ({
  tooltip: { 
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  legend: {
    data: ['区内企业', '区外企业']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: monthData,
    axisLabel: {
      interval: 0
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: value => formatNumber(value)
    }
  },
  series: [
    {
      name: '区内企业',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: props.data.monthlyTrend.map(item => item.inside)
    },
    {
      name: '区外企业',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: props.data.monthlyTrend.map(item => item.outside)
    }
  ]
}))
</script>

<style scoped>
.erp-network-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.stat-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.chart {
  height: 100px;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--grid-gap);
  flex: 1;
}

.chart-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.chart-content {
  height: 300px;
}
</style>