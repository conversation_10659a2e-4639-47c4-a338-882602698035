import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const buildProject = async () => {
  console.log('🚀 开始构建海关领导视窗系统')
  
  const startTime = Date.now()
  
  try {
    // 创建dist目录
    const distPath = path.resolve(__dirname, '../dist')
    if (!fs.existsSync(distPath)) {
      fs.mkdirSync(distPath)
    }
    
    // 复制静态资源
    const staticFiles = [
      'index.html',
      'favicon.ico'
    ]
    
    staticFiles.forEach(file => {
      const srcPath = path.resolve(__dirname, `../src/${file}`)
      const destPath = path.resolve(distPath, file)
      
      if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, destPath)
      }
    })
    
    // 生成构建信息
    const buildInfo = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'production'
    }
    
    fs.writeFileSync(
      path.resolve(distPath, 'build-info.json'),
      JSON.stringify(buildInfo, null, 2)
    )
    
    const duration = Date.now() - startTime
    console.log(`✅ 构建完成，耗时 ${duration}ms`)
    
  } catch (error) {
    console.error('❌ 构建失败:', error)
    process.exit(1)
  }
}

buildProject() 