<template>
  <div class="enterprise-stats" :class="theme">
    <!-- 数据卡片 -->
    <div class="stats-cards">
      <el-card v-for="stat in statsData" :key="stat.key" class="stat-card">
        <template #header>
          <div class="card-header">
            <span>{{ stat.title }}</span>
            <el-tag :type="stat.tagType" effect="dark">{{ stat.trend }}</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ stat.value }} {{ stat.unit }}</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'line',
                  data: stat.chartData,
                  smooth: true,
                  showSymbol: false,
                  lineStyle: { width: 3 },
                  areaStyle: {
                    opacity: 0.1
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>企业规模分布</span>
            <el-tag type="success" effect="dark">实时统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="scaleDistributionChartOption"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>企业增长趋势</span>
            <el-tag type="primary" effect="dark">近6月统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="growthTrendChartOption"
            autoresize
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores'
import { formatNumber } from '@/utils/format'

// 属性定义
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({
      total: 0,
      newCount: 0,
      erpRate: 0,
      scaleDistribution: [],
      growthTrend: []
    })
  }
})

// 主题状态
const themeStore = useThemeStore()
const theme = computed(() => themeStore.theme)

// 月份数据
const monthData = ['1月', '2月', '3月', '4月', '5月', '6月']

// 统计卡片数据
const statsData = computed(() => [
  {
    key: 'total_enterprises',
    title: '企业总数',
    value: formatNumber(props.data.total),
    unit: '家',
    trend: '持续增长',
    tagType: 'success',
    chartData: props.data.growthTrend.map(item => item.total)
  },
  {
    key: 'new_enterprises',
    title: '本月新增',
    value: formatNumber(props.data.newCount),
    unit: '家',
    trend: props.data.newCount > 0 ? '增速稳定' : '暂无新增',
    tagType: props.data.newCount > 0 ? 'success' : 'info',
    chartData: props.data.growthTrend.map(item => item.new)
  },
  {
    key: 'erp_ratio',
    title: 'ERP联网率',
    value: props.data.erpRate.toFixed(1),
    unit: '%',
    trend: props.data.erpRate >= 90 ? '运行良好' : '需要改进',
    tagType: props.data.erpRate >= 90 ? 'success' : 'warning',
    chartData: props.data.growthTrend.map(item => item.erpRate)
  }
])

// 企业规模分布图表配置
const scaleDistributionChartOption = computed(() => ({
  tooltip: { 
    trigger: 'item',
    formatter: '{b}: {c} 家 ({d}%)'
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'center',
    formatter: name => `${name}: ${props.data.scaleDistribution.find(item => item.name === name)?.value || 0} 家`
  },
  series: [{
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['40%', '50%'],
    data: props.data.scaleDistribution,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    label: {
      show: true,
      formatter: '{b}: {d}%'
    }
  }]
}))

// 企业增长趋势图表配置
const growthTrendChartOption = computed(() => ({
  tooltip: { 
    trigger: 'axis',
    formatter: params => {
      return params.map(param => {
        return `${param.name}<br/>${param.seriesName}: ${formatNumber(param.value)} 家`
      }).join('<br/>')
    }
  },
  legend: {
    data: ['新增企业', '累计企业']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: monthData,
    axisLabel: {
      interval: 0
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '新增企业',
      position: 'left',
      axisLabel: {
        formatter: value => formatNumber(value)
      }
    },
    {
      type: 'value',
      name: '累计企业',
      position: 'right',
      axisLabel: {
        formatter: value => formatNumber(value)
      }
    }
  ],
  series: [
    {
      name: '新增企业',
      type: 'bar',
      data: props.data.growthTrend.map(item => item.new),
      barWidth: '30%',
      yAxisIndex: 0,
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      },
      label: {
        show: true,
        position: 'top',
        formatter: value => formatNumber(value)
      }
    },
    {
      name: '累计企业',
      type: 'line',
      data: props.data.growthTrend.map(item => item.total),
      smooth: true,
      yAxisIndex: 1,
      label: {
        show: true,
        position: 'top',
        formatter: value => formatNumber(value)
      }
    }
  ]
}))
</script>

<style scoped>
.enterprise-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.stat-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.chart {
  height: 100px;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--grid-gap);
  flex: 1;
}

.chart-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.chart-content {
  height: 300px;
}
</style>