import { defineStore } from 'pinia'

// 主题状态管理
export const useThemeStore = defineStore('theme', {
  state: () => ({
    // 当前主题：light 或 dark
    currentTheme: 'light',
  }),

  getters: {
    // 主题名称
    theme: (state) => state.currentTheme,
    
    // 是否为暗色主题
    isDark: (state) => state.currentTheme === 'dark',
  },

  actions: {
    // 切换主题
    toggleTheme() {
      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light'
      this.applyTheme()
    },

    // 设置主题
    setTheme(theme) {
      if (theme === 'light' || theme === 'dark') {
        this.currentTheme = theme
        this.applyTheme()
      }
    },

    // 应用主题到文档
    applyTheme() {
      document.documentElement.setAttribute('data-theme', this.currentTheme)
      
      // 存储主题设置到本地存储
      localStorage.setItem('app-theme', this.currentTheme)
    },

    // 初始化主题
    initTheme() {
      // 从本地存储获取主题设置
      const savedTheme = localStorage.getItem('app-theme')
      
      // 如果有保存的主题设置，则使用它
      if (savedTheme) {
        this.currentTheme = savedTheme
      } else {
        // 否则检查系统偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        this.currentTheme = prefersDark ? 'dark' : 'light'
      }
      
      // 应用主题
      this.applyTheme()
      
      // 监听系统主题变化
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('app-theme')) {
          this.currentTheme = e.matches ? 'dark' : 'light'
          this.applyTheme()
        }
      })
    }
  }
})