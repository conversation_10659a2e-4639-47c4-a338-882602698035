/**
 * 格式化工具函数
 */

// 数字格式化
export const formatNumber = (
  value: number,
  options: {
    precision?: number
    unit?: string
    separator?: boolean
    prefix?: string
    suffix?: string
  } = {}
): string => {
  const {
    precision = 2,
    unit = '',
    separator = true,
    prefix = '',
    suffix = ''
  } = options

  if (isNaN(value)) return '--'

  let formatted = value.toFixed(precision)
  
  if (separator) {
    formatted = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  return `${prefix}${formatted}${unit}${suffix}`
}

// 大数字格式化（万、亿）
export const formatLargeNumber = (value: number, precision = 2): string => {
  if (isNaN(value)) return '--'
  
  if (value >= 100000000) {
    return `${(value / 100000000).toFixed(precision)}亿`
  } else if (value >= 10000) {
    return `${(value / 10000).toFixed(precision)}万`
  } else {
    return formatNumber(value, { precision })
  }
}

// 百分比格式化
export const formatPercentage = (
  value: number,
  precision = 2,
  showSign = false
): string => {
  if (isNaN(value)) return '--'
  
  const formatted = (value * 100).toFixed(precision)
  const sign = showSign && value > 0 ? '+' : ''
  return `${sign}${formatted}%`
}

// 货币格式化
export const formatCurrency = (
  value: number,
  currency = '¥',
  precision = 2
): string => {
  if (isNaN(value)) return '--'
  
  return `${currency}${formatNumber(value, { precision, separator: true })}`
}

// 日期格式化
export const formatDate = (
  date: Date | string | number,
  format = 'YYYY-MM-DD'
): string => {
  const d = new Date(date)
  if (isNaN(d.getTime())) return '--'

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

// 相对时间格式化
export const formatRelativeTime = (date: Date | string | number): string => {
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else {
    return formatDate(d, 'YYYY-MM-DD')
  }
}

// 文件大小格式化
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 趋势格式化
export const formatTrend = (
  value: number,
  options: {
    showIcon?: boolean
    showSign?: boolean
    precision?: number
  } = {}
): { text: string; icon: string; color: string } => {
  const { showIcon = true, showSign = true, precision = 2 } = options
  
  if (isNaN(value)) {
    return { text: '--', icon: '', color: '#666' }
  }

  const absValue = Math.abs(value)
  const sign = showSign ? (value > 0 ? '+' : value < 0 ? '-' : '') : ''
  const text = `${sign}${absValue.toFixed(precision)}%`
  
  if (value > 0) {
    return {
      text,
      icon: showIcon ? '↗' : '',
      color: '#52c41a'
    }
  } else if (value < 0) {
    return {
      text,
      icon: showIcon ? '↘' : '',
      color: '#f5222d'
    }
  } else {
    return {
      text,
      icon: showIcon ? '→' : '',
      color: '#666'
    }
  }
}

// 单位转换
export const convertUnit = (
  value: number,
  fromUnit: string,
  toUnit: string
): number => {
  // 重量单位转换
  const weightUnits: Record<string, number> = {
    'g': 1,
    'kg': 1000,
    't': 1000000
  }
  
  // 长度单位转换
  const lengthUnits: Record<string, number> = {
    'mm': 1,
    'cm': 10,
    'm': 1000,
    'km': 1000000
  }
  
  // 面积单位转换
  const areaUnits: Record<string, number> = {
    'mm²': 1,
    'cm²': 100,
    'm²': 1000000,
    'km²': *************
  }

  const units = { ...weightUnits, ...lengthUnits, ...areaUnits }
  
  if (units[fromUnit] && units[toUnit]) {
    return (value * units[fromUnit]) / units[toUnit]
  }
  
  return value
}

// 数据脱敏
export const maskSensitiveData = (
  data: string,
  type: 'phone' | 'email' | 'idCard' | 'bankCard' = 'phone'
): string => {
  if (!data) return ''
  
  switch (type) {
    case 'phone':
      return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'email':
      return data.replace(/(.{2}).*(@.*)/, '$1****$2')
    case 'idCard':
      return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    case 'bankCard':
      return data.replace(/(\d{4})\d{8,12}(\d{4})/, '$1********$2')
    default:
      return data
  }
}
