#!/bin/bash

# 部署脚本配置
REMOTE_SERVER="<EMAIL>"
REMOTE_PATH="/var/www/port-leadership-window"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

# 构建项目
build_project() {
    echo -e "${GREEN}开始构建项目...${NC}"
    npm run build
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}项目构建成功${NC}"
    else
        echo -e "${RED}项目构建失败${NC}"
        exit 1
    fi
}

# 运行单元测试
run_tests() {
    echo -e "${GREEN}运行单元测试...${NC}"
    npm run test
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}所有测试通过${NC}"
    else
        echo -e "${RED}存在测试失败，请检查${NC}"
        exit 1
    fi
}

# 同步到远程服务器
sync_to_server() {
    echo -e "${GREEN}开始同步到远程服务器...${NC}"
    
    # 同步dist目录和服务器文件
    rsync -avz --delete \
        --exclude '.git' \
        --exclude 'node_modules' \
        -e ssh \
        ./dist/ \
        ./server/ \
        ./package.json \
        "$REMOTE_SERVER:$REMOTE_PATH"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}文件同步成功${NC}"
    else
        echo -e "${RED}文件同步失败${NC}"
        exit 1
    fi
}

# 远程服务器部署
remote_deploy() {
    ssh "$REMOTE_SERVER" << ENDSSH
        cd "$REMOTE_PATH"
        npm install --production
        pm2 restart port-leadership-window
ENDSSH
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}远程部署成功${NC}"
    else
        echo -e "${RED}远程部署失败${NC}"
        exit 1
    fi
}

# 主部署流程
main() {
    echo -e "${GREEN}开始海关领导视窗系统部署${NC}"
    
    run_tests
    build_project
    sync_to_server
    remote_deploy
    
    echo -e "${GREEN}部署全流程完成！${NC}"
}

main 