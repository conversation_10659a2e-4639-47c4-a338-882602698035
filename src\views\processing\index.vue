<template>
  <div class="processing-container">
    <component :is="currentView" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import PCView from './pc/index.vue'
import MobileView from './mobile/index.vue'
import WallView from './wall/index.vue'

const route = useRoute()

const currentView = computed(() => {
  const device = route.query.device || 'pc'
  switch (device) {
    case 'mobile':
      return MobileView
    case 'wall':
      return WallView
    default:
      return PCView
  }
})
</script>

<style scoped>
.processing-container {
  height: 100%;
  width: 100%;
}
</style>