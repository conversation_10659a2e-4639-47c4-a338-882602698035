# 海南海关领导视窗系统

## 项目简介
海南海关领导视窗系统，专注于展示加工增值和交通运输工具相关数据的大屏系统。

## 技术栈
- **前端**: Vue 3.5 + ECharts 5.5
- **后端**: Node.js + Express (开发服务器)
- **样式**: 原生CSS (无预处理器)
- **兼容性**: 支持 Mobile、PC、Wall 三种设备

## 项目结构
```
├── index.html          # 主页面文件
├── server.js           # 开发服务器 (支持热重载)
├── package.json        # 项目配置
├── .cursor/rules/      # 需求文档
└── README.md          # 项目说明
```

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
# 或
npm start
```

访问: http://localhost:3000

### 热重载
修改 `index.html` 文件后，控制台会提示文件更新，刷新浏览器即可看到更改。

## 功能模块

### 1. 加工增值
- ERP联网企业数量统计
- 加工增值总额展示
- 免征税款统计
- 备案企业数量
- TOP5关区企业分布图表
- TOP5企业产值排行图表

### 2. 交通运输工具
- 零关税进口车辆统计
- 减免税费总额
- 车辆类型分布
- 车辆类型分布饼图
- 减免税费企业排行柱状图

## 响应式设计
- **Mobile (≤768px)**: 单列布局，图表高度250px
- **PC (769px-1920px)**: 三列网格布局，图表高度350px  
- **Wall (≥1921px)**: 四列网格布局，图表高度400px

## 开发说明
- 所有代码集中在 `index.html` 文件中
- 使用CDN引入Vue和ECharts，确保兼容性
- 样式采用内联CSS，避免外部依赖
- 数据为模拟数据，可根据实际API调整

## 部署
项目可直接部署到任何支持静态文件的服务器，只需要 `index.html` 文件即可。
