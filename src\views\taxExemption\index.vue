<template>
  <div class="tax-exemption-view">
    <div v-if="taxExemptionStore.loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="taxExemptionStore.error" class="error-container">
      <el-alert
        title="加载失败"
        type="error"
        :description="taxExemptionStore.error"
        show-icon
      />
    </div>
    <div v-else>
      <!-- 顶部统计卡片 -->
      <div class="stats-cards">
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>免征税款总额</span>
            </div>
          </template>
          <div class="card-value">{{ taxExemptionStore.stats.totalExemption }} 万元</div>
          <div class="card-footer">
            <span class="trend-text">同比增长 {{ taxExemptionStore.stats.yearOverYear }}%</span>
          </div>
        </el-card>
        
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>免征税款单数</span>
            </div>
          </template>
          <div class="card-value">{{ taxExemptionStore.stats.totalCount }} 单</div>
          <div class="card-footer">
            <span class="trend-text">月均 {{ Math.round(taxExemptionStore.stats.totalCount / 6) }} 单</span>
          </div>
        </el-card>

        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>平均单值</span>
            </div>
          </template>
          <div class="card-value">
            {{ formatValue(taxExemptionStore.stats.totalExemption / taxExemptionStore.stats.totalCount) }} 万元
          </div>
          <div class="card-footer">
            <span class="trend-text">同比增长 {{ (taxExemptionStore.stats.yearOverYear - 2).toFixed(1) }}%</span>
          </div>
        </el-card>

        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>企业数量</span>
            </div>
          </template>
          <div class="card-value">{{ taxExemptionStore.details.enterpriseRanking?.length || 0 }} 家</div>
          <div class="card-footer">
            <span class="trend-text">同比增长 5.2%</span>
          </div>
        </el-card>

        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>免税类型数</span>
            </div>
          </template>
          <div class="card-value">{{ taxExemptionStore.stats.typeDistribution?.length || 0 }} 种</div>
          <div class="card-footer">
            <span class="trend-text">较上月增加 1 种</span>
          </div>
        </el-card>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="charts-row">
          <ChartCard 
            title="免征税款月度趋势" 
            :options="trendChartOptions" 
          />
          
          <ChartCard 
            title="免征税款类型分布" 
            :options="typeChartOptions" 
          />
        </div>

        <div class="charts-row">
          <ChartCard 
            title="区域分布" 
            :options="regionChartOptions" 
          />
          
          <ChartCard 
            title="免征税款TOP5企业" 
            :options="topCompaniesOptions" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useTaxExemptionStore } from '../../stores/taxExemption.js';
import ChartCard from '../../components/ChartCard.vue';
import { ElCard, ElLoading, ElMessage } from 'element-plus';

const taxExemptionStore = useTaxExemptionStore();

// 格式化数值
const formatValue = (value) => {
  return value.toLocaleString('zh-CN', { maximumFractionDigits: 2 });
};

// 免征税款趋势图表配置
const trendChartOptions = computed(() => {
  const months = taxExemptionStore.stats.monthlyTrend?.map(item => `${item.month}月`) || [];
  const values = taxExemptionStore.stats.monthlyTrend?.map(item => item.value) || [];
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        color: '#909399'
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#909399'
      },
      splitLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    series: [
      {
        name: '免征税款',
        type: 'line',
        smooth: true,
        data: values,
        itemStyle: {
          color: '#67C23A'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(103, 194, 58, 0.5)'
            }, {
              offset: 1, color: 'rgba(103, 194, 58, 0.1)'
            }]
          }
        }
      }
    ]
  };
});

// 免征税款类型分布图表配置
const typeChartOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#606266'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: taxExemptionStore.stats.typeDistribution?.map(item => ({
          name: item.name,
          value: item.value
        })) || []
      }
    ]
  };
});

// 区域分布图表配置
const regionChartOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#606266'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: taxExemptionStore.details.regionDistribution?.map(item => ({
          name: item.name,
          value: item.value
        })) || []
      }
    ]
  };
});

// TOP5企业图表配置
const topCompaniesOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: '#909399'
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: taxExemptionStore.details.enterpriseRanking?.map(item => item.name) || [],
      axisLabel: {
        color: '#909399'
      },
      splitLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    series: [
      {
        name: '免征税款(万元)',
        type: 'bar',
        barWidth: '40%',
        data: taxExemptionStore.details.enterpriseRanking?.map(item => item.value) || [],
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  };
});

// 获取数据
// 在组件挂载时加载数据
onMounted(async () => {
  await taxExemptionStore.fetchStats();
});
</script>

<style scoped>
.tax-exemption-view {
  width: 100%;
}

.stats-cards {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.stats-card {
  flex: 1;
  min-width: 180px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #67C23A;
  text-align: center;
  padding: 10px 0;
}

.card-footer {
  text-align: center;
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.trend-text {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.charts-row {
  display: flex;
  gap: 20px;
  margin-bottom: 0;
}

.charts-row > * {
  flex: 1;
}

/* 移动设备适配 */
@media screen and (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }
  
  .charts-row {
    flex-direction: column;
  }
  
  .stats-card {
    min-width: 100%;
  }
}

/* 大屏设备适配 */
@media screen and (min-width: 1920px) {
  .stats-cards {
    gap: 20px;
  }
  
  .charts-container {
    gap: 30px;
  }
  
  .charts-row {
    gap: 30px;
  }
  
  .card-value {
    font-size: 28px;
  }
  
  .card-footer {
    font-size: 16px;
  }
}
</style>