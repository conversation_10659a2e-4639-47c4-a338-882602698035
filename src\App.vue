<template>
  <div id="app" class="min-h-screen bg-gray-900 text-white">
    <!-- 布局切换按钮 -->
    <div class="fixed top-4 right-4 z-50 flex gap-2">
      <button 
        v-for="layout in layouts" 
        :key="layout.name"
        @click="currentLayout = layout.name"
        :class="[
          'px-4 py-2 rounded-lg font-bold transition-colors',
          currentLayout === layout.name
            ? 'bg-blue-500 text-white'
            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
        ]"
      >
        {{ layout.label }}
      </button>
    </div>

    <!-- 加工增值业务视图 -->
    <div class="container mx-auto px-4 py-8">
      <h2 class="text-2xl font-bold mb-8">加工增值业务</h2>
      
      <!-- 网格布局 -->
      <div :class="layoutClass">
        <!-- ERP联网企业数量 -->
        <div class="bg-gray-800 rounded-lg p-6" :class="cardClass">
          <h3 class="text-xl font-bold mb-4">ERP联网企业数量</h3>
          <div class="chart-container" ref="erpChart"></div>
        </div>

        <!-- 主要地区市县分布 -->
        <div class="bg-gray-800 rounded-lg p-6" :class="cardClass">
          <h3 class="text-xl font-bold mb-4">主要地区市县分布</h3>
          <div class="chart-container" ref="regionChart"></div>
        </div>

        <!-- 加工增值产值TOP5企业 -->
        <div class="bg-gray-800 rounded-lg p-6" :class="cardClass">
          <h3 class="text-xl font-bold mb-4">加工增值产值TOP5企业</h3>
          <div class="chart-container" ref="topChart"></div>
        </div>

        <!-- 主要加工产品TOP5 -->
        <div class="bg-gray-800 rounded-lg p-6" :class="cardClass">
          <h3 class="text-xl font-bold mb-4">主要加工产品TOP5</h3>
          <div class="chart-container" ref="productChart"></div>
        </div>

        <!-- 免征税额月度趋势 -->
        <div class="bg-gray-800 rounded-lg p-6" :class="cardClass">
          <h3 class="text-xl font-bold mb-4">免征税额月度趋势</h3>
          <div class="chart-container" ref="taxChart"></div>
        </div>

        <!-- 重点企业类型分布 -->
        <div class="bg-gray-800 rounded-lg p-6" :class="cardClass">
          <h3 class="text-xl font-bold mb-4">重点企业类型分布</h3>
          <div class="chart-container" ref="typeChart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useDataService } from '@/composables/useDataService'
import { useChartConfig } from '@/composables/useChartConfig'
import * as echarts from 'echarts'

// 布局配置
const layouts = [
  { name: 'grid', label: '网格布局' },
  { name: 'horizontal', label: '横向布局' },
  { name: 'vertical', label: '纵向布局' },
  { name: 'wall', label: '大屏布局' }
]

// 当前布局
const currentLayout = ref('grid')

// 布局类名
const layoutClass = computed(() => {
  switch (currentLayout.value) {
    case 'grid':
      return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
    case 'horizontal':
      return 'flex flex-nowrap overflow-x-auto gap-6 pb-4'
    case 'vertical':
      return 'flex flex-col gap-6'
    case 'wall':
      return 'grid grid-cols-3 grid-rows-2 gap-6 h-screen'
    default:
      return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
  }
})

// 卡片类名
const cardClass = computed(() => {
  switch (currentLayout.value) {
    case 'horizontal':
      return 'min-w-[400px]'
    case 'wall':
      return 'h-full'
    default:
      return ''
  }
})

// 图表引用
const erpChart = ref(null)
const topChart = ref(null)
const productChart = ref(null)
const taxChart = ref(null)
const regionChart = ref(null)
const typeChart = ref(null)

// 数据服务
const { loadData, startAutoUpdate } = useDataService()

// 图表配置
const { getConfig } = useChartConfig()

// 图表实例
let charts = {}

// 更新图表数据
const updateCharts = (data) => {
  // 更新ERP联网企业数量
  charts.erp.setOption({
    series: [{
      data: data.erp.data
    }]
  })

  // 更新加工增值产值TOP5企业
  charts.top.setOption({
    series: [{
      data: data.top.data
    }]
  })

  // 更新主要加工产品TOP5
  charts.product.setOption({
    series: [{
      data: data.product.data
    }]
  })

  // 更新免征税额月度趋势
  charts.tax.setOption({
    series: [{
      data: data.tax.data
    }]
  })

  // 更新主要地区市县分布
  charts.region.setOption({
    series: [{
      data: data.region.data
    }]
  })

  // 更新重点企业类型分布
  charts.type.setOption({
    series: [{
      data: data.type.data
    }]
  })
}

// 初始化图表
const initCharts = async () => {
  // 初始化各个图表实例
  charts = {
    erp: echarts.init(erpChart.value, null, { renderer: 'canvas' }),
    top: echarts.init(topChart.value, null, { renderer: 'canvas' }),
    product: echarts.init(productChart.value, null, { renderer: 'canvas' }),
    tax: echarts.init(taxChart.value, null, { renderer: 'canvas' }),
    region: echarts.init(regionChart.value, null, { renderer: 'canvas' }),
    type: echarts.init(typeChart.value, null, { renderer: 'canvas' })
  }

  // 设置图表配置
  charts.erp.setOption(getConfig('pie'))
  charts.top.setOption(getConfig('bar'))
  charts.product.setOption(getConfig('bar'))
  charts.tax.setOption(getConfig('line'))
  charts.region.setOption(getConfig('pie'))
  charts.type.setOption(getConfig('pie'))

  // 加载初始数据
  const data = await loadData()
  updateCharts(data)

  // 启动自动更新
  const stopAutoUpdate = startAutoUpdate(updateCharts, 5000)
  onUnmounted(() => stopAutoUpdate())
}

// 响应式调整
const handleResize = () => {
  Object.values(charts).forEach(chart => chart.resize())
}

// 销毁图表
const destroyCharts = () => {
  Object.values(charts).forEach(chart => chart.dispose())
  charts = {}
}

onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  destroyCharts()
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}

@media (max-width: 1024px) {
  .chart-container {
    height: 300px;
  }
}

@media (max-width: 640px) {
  .chart-container {
    height: 250px;
  }
}

/* 横向滚动布局样式 */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* 大屏布局样式 */
.h-screen {
  height: calc(100vh - 8rem);
}
</style>