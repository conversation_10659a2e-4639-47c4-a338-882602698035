<template>
  <div id="app">
    <ProcessingView v-if="view==='processing'" />
    <TransportView v-else />
  </div>
</template>

<script>
import { ref } from 'vue'
import { isMobile } from './utils/device'
import ProcessingView from './views/ProcessingView.vue'
import TransportView from './views/TransportView.vue'

export default {
  name: 'App',
  components: { ProcessingView, TransportView },
  setup() {
    // 这里可根据实际业务切换视图
    const view = ref(isMobile() ? 'processing' : 'transport')
    return { view }
  }
}
</script>

<style>
#app { width: 100vw; height: 100vh; }
</style>