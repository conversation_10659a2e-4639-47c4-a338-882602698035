import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime
import logging

def validate_numeric(data: pd.Series) -> bool:
    """Validate if a series contains only numeric values"""
    return pd.to_numeric(data, errors='coerce').notna().all()

def calculate_moving_average(data: pd.Series, window: int = 3) -> pd.Series:
    """Calculate moving average for a series"""
    return data.rolling(window=window).mean()

def format_timestamp(dt: datetime) -> str:
    """Format datetime to standard string format"""
    return dt.strftime("%Y-%m-%d %H:%M:%S")

class DataProcessor:
    def __init__(self):
        self.data = None
        
    def load_data(self, filepath: str) -> None:
        """Load data from file"""
        try:
            self.data = pd.read_csv(filepath)
        except Exception as e:
            logging.error(f"Error loading data: {str(e)}")
            raise
            
    def clean_data(self) -> pd.DataFrame:
        """Clean and prepare data"""
        if self.data is None:
            raise ValueError("No data loaded")
            
        # Remove duplicates
        self.data = self.data.drop_duplicates()
        
        # Handle missing values
        self.data = self.data.fillna(0)
        
        return self.data

class BusinessLogic:
    def __init__(self, data_processor: DataProcessor):
        self.data_processor = data_processor
        
    def process_business_rules(self, rules: Dict[str, Any]) -> pd.DataFrame:
        """Apply business rules to processed data"""
        processed_data = self.data_processor.clean_data()
        
        # Apply business rules
        if rules.get('filter_threshold'):
            processed_data = processed_data[
                processed_data['value'] > rules['filter_threshold']
            ]
            
        if rules.get('calculate_ma'):
            processed_data['moving_avg'] = calculate_moving_average(
                processed_data['value']
            )
            
        return processed_data

def main():
    # Initialize components
    data_processor = DataProcessor()
    business_logic = BusinessLogic(data_processor)
    
    try:
        # Load and process data
        data_processor.load_data('sample_data.csv')
        
        # Define business rules
        rules = {
            'filter_threshold': 100,
            'calculate_ma': True
        }
        
        # Process data through business logic
        result = business_logic.process_business_rules(rules)
        
        # Output results
        print("Processing complete. Results shape:", result.shape)
        return result
        
    except Exception as e:
        logging.error(f"Error in main execution: {str(e)}")
        raise

if __name__ == "__main__":
    result_df = main()