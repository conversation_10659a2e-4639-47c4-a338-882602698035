/**
 * HTTP请求工具
 */

import type { ApiResponse, RequestConfig, ApiError } from '../types/api'
import { API_CONFIG, STATUS_CODES } from '../constants'

// 请求缓存
const requestCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

// 生成缓存键
const generateCacheKey = (config: RequestConfig): string => {
  return `${config.method}_${config.url}_${JSON.stringify(config.params || {})}`
}

// 检查缓存
const getFromCache = (key: string): any | null => {
  const cached = requestCache.get(key)
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data
  }
  requestCache.delete(key)
  return null
}

// 设置缓存
const setCache = (key: string, data: any, ttl: number): void => {
  requestCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  })
}

// 请求重试
const retryRequest = async (
  config: RequestConfig,
  retryCount = 0
): Promise<any> => {
  try {
    return await makeRequest(config)
  } catch (error) {
    if (retryCount < (config.retry || API_CONFIG.RETRY_TIMES)) {
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)))
      return retryRequest(config, retryCount + 1)
    }
    throw error
  }
}

// 核心请求函数
const makeRequest = async (config: RequestConfig): Promise<any> => {
  const {
    url,
    method = 'GET',
    params,
    data,
    headers = {},
    timeout = API_CONFIG.TIMEOUT
  } = config

  // 构建完整URL
  const baseURL = API_CONFIG.BASE_URL
  let fullUrl = `${baseURL}${url}`

  // 处理GET请求参数
  if (method === 'GET' && params) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    const queryString = searchParams.toString()
    if (queryString) {
      fullUrl += `?${queryString}`
    }
  }

  // 设置请求头
  const requestHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    ...headers
  }

  // 获取用户token
  const token = localStorage.getItem('customs_user_token')
  if (token) {
    requestHeaders.Authorization = `Bearer ${token}`
  }

  // 构建fetch选项
  const fetchOptions: RequestInit = {
    method,
    headers: requestHeaders,
    signal: AbortSignal.timeout(timeout)
  }

  // 处理请求体
  if (method !== 'GET' && data) {
    fetchOptions.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(fullUrl, fetchOptions)
    
    // 检查响应状态
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Request failed:', error)
    throw error
  }
}

// 主要的请求函数
export const request = async <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
  try {
    // 检查缓存
    if (config.cache && config.method === 'GET') {
      const cacheKey = generateCacheKey(config)
      const cached = getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    // 发送请求
    const response = await (config.retry ? retryRequest(config) : makeRequest(config))

    // 设置缓存
    if (config.cache && config.method === 'GET' && response.success) {
      const cacheKey = generateCacheKey(config)
      setCache(cacheKey, response, config.cacheTTL || 300000) // 默认5分钟
    }

    return response
  } catch (error) {
    const apiError: ApiError = {
      code: STATUS_CODES.SERVER_ERROR,
      message: error instanceof Error ? error.message : '请求失败',
      timestamp: Date.now()
    }
    
    return {
      code: apiError.code,
      message: apiError.message,
      data: null,
      success: false,
      timestamp: apiError.timestamp
    }
  }
}

// GET请求
export const get = <T = any>(
  url: string,
  params?: Record<string, any>,
  options?: Partial<RequestConfig>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...options
  })
}

// POST请求
export const post = <T = any>(
  url: string,
  data?: any,
  options?: Partial<RequestConfig>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
export const put = <T = any>(
  url: string,
  data?: any,
  options?: Partial<RequestConfig>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
export const del = <T = any>(
  url: string,
  params?: Record<string, any>,
  options?: Partial<RequestConfig>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'DELETE',
    params,
    ...options
  })
}

// 文件上传
export const upload = async (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<ApiResponse> => {
  const formData = new FormData()
  formData.append('file', file)

  const xhr = new XMLHttpRequest()
  
  return new Promise((resolve, reject) => {
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = (event.loaded / event.total) * 100
        onProgress(progress)
      }
    })

    xhr.addEventListener('load', () => {
      try {
        const response = JSON.parse(xhr.responseText)
        resolve(response)
      } catch (error) {
        reject(new Error('响应解析失败'))
      }
    })

    xhr.addEventListener('error', () => {
      reject(new Error('上传失败'))
    })

    xhr.open('POST', `${API_CONFIG.BASE_URL}${url}`)
    
    const token = localStorage.getItem('customs_user_token')
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }
    
    xhr.send(formData)
  })
}

// 清除缓存
export const clearCache = (pattern?: string): void => {
  if (pattern) {
    for (const key of requestCache.keys()) {
      if (key.includes(pattern)) {
        requestCache.delete(key)
      }
    }
  } else {
    requestCache.clear()
  }
}
