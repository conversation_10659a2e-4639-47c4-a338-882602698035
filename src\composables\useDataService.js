import { ref } from 'vue'

export function useDataService() {
  // 模拟数据生成
  const generateRandomData = () => {
    return {
      erp: {
        data: [
          { value: Math.floor(Math.random() * 1000 + 500), name: '海口' },
          { value: Math.floor(Math.random() * 800 + 300), name: '三亚' },
          { value: Math.floor(Math.random() * 600 + 200), name: '儋州' },
          { value: Math.floor(Math.random() * 400 + 100), name: '琼海' },
          { value: Math.floor(Math.random() * 300 + 100), name: '文昌' }
        ]
      },
      top: {
        data: [
          Math.floor(Math.random() * 300 + 150),
          Math.floor(Math.random() * 250 + 120),
          Math.floor(Math.random() * 200 + 100),
          Math.floor(Math.random() * 180 + 80),
          Math.floor(Math.random() * 150 + 50)
        ]
      },
      product: {
        data: [
          Math.floor(Math.random() * 250 + 100),
          Math.floor(Math.random() * 220 + 90),
          Math.floor(Math.random() * 190 + 80),
          Math.floor(Math.random() * 160 + 70),
          Math.floor(Math.random() * 130 + 60)
        ]
      },
      tax: {
        data: [
          Math.floor(Math.random() * 200 + 100),
          Math.floor(Math.random() * 250 + 150),
          Math.floor(Math.random() * 220 + 130),
          Math.floor(Math.random() * 210 + 120),
          Math.floor(Math.random() * 180 + 90),
          Math.floor(Math.random() * 160 + 80)
        ]
      },
      region: {
        data: [
          { value: Math.floor(Math.random() * 1200 + 800), name: '海口区域' },
          { value: Math.floor(Math.random() * 1000 + 600), name: '三亚区域' },
          { value: Math.floor(Math.random() * 800 + 400), name: '洋浦区域' },
          { value: Math.floor(Math.random() * 600 + 200), name: '琼海区域' }
        ]
      },
      type: {
        data: [
          { value: Math.floor(Math.random() * 900 + 600), name: '加工贸易' },
          { value: Math.floor(Math.random() * 700 + 400), name: '保税物流' },
          { value: Math.floor(Math.random() * 500 + 200), name: '跨境电商' },
          { value: Math.floor(Math.random() * 300 + 100), name: '其他业务' }
        ]
      }
    }
  }

  // 加载数据
  const loadData = () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(generateRandomData())
      }, 500)
    })
  }

  // 自动更新数据
  const startAutoUpdate = (callback, interval = 5000) => {
    const timer = setInterval(async () => {
      const data = await loadData()
      callback(data)
    }, interval)

    return () => clearInterval(timer)
  }

  return {
    loadData,
    startAutoUpdate
  }
}