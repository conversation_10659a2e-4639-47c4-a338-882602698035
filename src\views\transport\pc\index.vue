<template>
  <div class="transport-pc-view">
    <TransportChart :data="chartData" />
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useTransportData } from '../../../middleware/transport'
import TransportChart from '../../../components/TransportChart.vue'

const chartData = ref({})
onMounted(async () => {
  chartData.value = await useTransportData()
})
</script>
<style scoped>
.transport-pc-view { width: 100vw; height: 100vh; background: #101c2a; }
</style> 