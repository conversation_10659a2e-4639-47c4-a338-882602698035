<template>
  <transport-base
    class="p-8 gap-8"
    charts-layout="grid-2-2"
    tables-layout="grid-2"
  />
</template>

<script setup>
import TransportBase from '../components/TransportBase.vue'
</script>

<style scoped>
:deep(.stats-section) {
  grid-template-columns: repeat(5, 1fr);
}

:deep(.chart-wrapper),
:deep(.table-wrapper) {
  padding: 1.5rem;
}

:deep(.chart-title),
:deep(.table-title) {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
}

:deep(.data-table th),
:deep(.data-table td) {
  font-size: 1rem;
  padding: 1rem;
}
</style>