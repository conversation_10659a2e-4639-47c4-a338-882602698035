<template>
  <div class="company-rank-stats">
    <!-- 数据卡片 -->
    <div class="data-cards">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="data-card">
              <div class="card-title">企业总数</div>
              <div class="card-value">{{ loading ? '-' : '328' }}</div>
              <div class="card-trend up">
                <el-icon><CaretTop /></el-icon>
                <span>较上月 +15</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="data-card">
              <div class="card-title">平均减免税额</div>
              <div class="card-value">{{ loading ? '-' : '￥285万' }}</div>
              <div class="card-trend up">
                <el-icon><CaretTop /></el-icon>
                <span>较上月 +5.2%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="data-card">
              <div class="card-title">最高减免税额</div>
              <div class="card-value">{{ loading ? '-' : '￥1,580万' }}</div>
              <div class="card-trend up">
                <el-icon><CaretTop /></el-icon>
                <span>较上月 +12.8%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="16">
        <!-- 企业排名TOP10 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">减免税额TOP10企业</div>
            <div class="chart" ref="rankingChart"></div>
          </div>
        </el-col>
        <!-- 企业分布 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">企业减免税额分布</div>
            <div class="chart" ref="distributionChart"></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 接收父组件传递的数据和加载状态
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// 图表实例
const rankingChart = ref(null)
const distributionChart = ref(null)
let rankingInstance = null
let distributionInstance = null

// 初始化企业排名图表
const initRankingChart = () => {
  if (!rankingChart.value) return

  rankingInstance = echarts.init(rankingChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'var(--text-color-secondary)'
        }
      },
      axisLabel: {
        color: 'var(--text-color)',
        formatter: '{value}万'
      }
    },
    yAxis: {
      type: 'category',
      data: [
        '企业A',
        '企业B',
        '企业C',
        '企业D',
        '企业E',
        '企业F',
        '企业G',
        '企业H',
        '企业I',
        '企业J'
      ],
      axisLine: {
        lineStyle: {
          color: 'var(--text-color-secondary)'
        }
      },
      axisLabel: {
        color: 'var(--text-color)'
      }
    },
    series: [
      {
        name: '减免税额',
        type: 'bar',
        data: [1580, 1320, 1150, 980, 850, 720, 650, 580, 520, 480],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  }
  rankingInstance.setOption(option)
}

// 初始化企业分布图表
const initDistributionChart = () => {
  if (!distributionChart.value) return

  distributionInstance = echarts.init(distributionChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}家 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: 'var(--text-color)'
      }
    },
    series: [
      {
        name: '企业分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: '1000万以上' },
          { value: 85, name: '500-1000万' },
          { value: 95, name: '100-500万' },
          { value: 75, name: '50-100万' },
          { value: 38, name: '50万以下' }
        ]
      }
    ]
  }
  distributionInstance.setOption(option)
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (!newData) return
    // 更新图表数据
    if (rankingInstance && distributionInstance) {
      // 这里可以根据实际数据更新图表
    }
  }
)

// 组件挂载时初始化图表
onMounted(() => {
  initRankingChart()
  initDistributionChart()

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    rankingInstance?.resize()
    distributionInstance?.resize()
  })
})

// 组件卸载时销毁图表实例
onUnmounted(() => {
  rankingInstance?.dispose()
  distributionInstance?.dispose()
  window.removeEventListener('resize', () => {
    rankingInstance?.resize()
    distributionInstance?.resize()
  })
})
</script>

<style scoped>
.company-rank-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.data-cards {
  margin-bottom: 20px;
}

.data-card {
  text-align: center;
  padding: 10px;
}

.card-title {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8px;
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.card-trend.up {
  color: #67c23a;
}

.card-trend.down {
  color: #f56c6c;
}

.charts-container {
  flex: 1;
  min-height: 0;
}

.chart-wrapper {
  height: 100%;
  background-color: var(--component-bg);
  border-radius: 4px;
  padding: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;
}

.chart {
  height: calc(100% - 32px);
}
</style>