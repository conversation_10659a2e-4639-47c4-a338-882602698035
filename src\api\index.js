import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    const message = error.response?.data?.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

// 加工增值数据
export async function fetchProcessingData() {
  return api.get('/processing')
}

// 交通运输数据
export async function fetchTransportData() {
  return api.get('/transport')
}