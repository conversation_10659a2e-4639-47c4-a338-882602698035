/**
 * 海关业务API接口
 * 与后端服务通信的统一接口
 */

import { get, post } from '../shared/utils/request.js'

// 加工增值数据API
export const processingAPI = {
  // 获取基础统计
  getBasicStats: () => get('/customs/processing/stats'),

  // 获取货物统计
  getGoodsStats: () => get('/customs/processing/goods'),

  // 获取免征税额
  getTaxExemption: () => get('/customs/processing/tax'),

  // 获取企业统计
  getEnterpriseStats: () => get('/customs/processing/enterprises'),

  // 获取月度趋势
  getValueTrend: () => get('/customs/processing/trend'),

  // 获取企业排行
  getEnterpriseRanking: () => get('/customs/processing/ranking'),

  // 获取ERP数据
  getERPData: () => get('/customs/processing/erp')
}

// 交通运输数据API
export const transportAPI = {
  // 获取零关税车辆
  getZeroTariffCars: () => get('/customs/transport/cars'),

  // 获取免税交通工具
  getDutyFreeVehicles: () => get('/customs/transport/vehicles'),

  // 获取零配件维修
  getSparePartsRepair: () => get('/customs/transport/spare-parts'),

  // 获取企业排行
  getEnterpriseRanking: () => get('/customs/transport/ranking')
}

// 综合分析API
export const analyticsAPI = {
  // 获取关键指标
  getKeyIndicators: () => get('/customs/analytics/indicators'),

  // 获取地区分布
  getRegionDistribution: () => get('/customs/analytics/regions'),

  // 获取时间趋势
  getTimeTrend: (params) => get('/customs/analytics/trend', params)
}

// 兼容原有接口
export const fetchProcessingData = processingAPI.getBasicStats
export const fetchTransportData = transportAPI.getZeroTariffCars

// 统一的数据获取接口
export const getProcessingData = async () => {
  try {
    const response = await processingAPI.getBasicStats()
    return response.success ? response.data : null
  } catch (error) {
    console.error('获取加工增值数据失败:', error)
    return null
  }
}

export const getTransportData = async () => {
  try {
    const response = await transportAPI.getZeroTariffCars()
    return response.success ? response.data : null
  } catch (error) {
    console.error('获取交通运输数据失败:', error)
    return null
  }
}