<template>
  <div class="processing-mobile-view">
    <ProcessingChart :data="chartData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProcessingData } from '../../../middleware/processing'
import ProcessingChart from '../../../components/ProcessingChart.vue'

const chartData = ref({})
onMounted(async () => {
  chartData.value = await useProcessingData()
})
</script>

<style scoped>
.processing-mobile-view { width: 100vw; height: 100vh; background: #101c2a; }
</style>