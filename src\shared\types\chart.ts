/**
 * 图表相关类型定义
 */

// 图表数据项类型
export interface ChartDataItem {
  name: string
  value: number
  [key: string]: any
}

// 图表系列类型
export interface ChartSeries {
  name: string
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'map' | 'gauge' | 'radar'
  data: ChartDataItem[]
  stack?: string
  smooth?: boolean
  areaStyle?: any
  itemStyle?: any
  lineStyle?: any
  label?: any
}

// 图表配置类型
export interface ChartConfig {
  title?: {
    text: string
    subtext?: string
    left?: string | number
    top?: string | number
    textStyle?: any
  }
  legend?: {
    show: boolean
    data?: string[]
    orient?: 'horizontal' | 'vertical'
    left?: string | number
    top?: string | number
  }
  grid?: {
    left?: string | number
    right?: string | number
    top?: string | number
    bottom?: string | number
    containLabel?: boolean
  }
  xAxis?: {
    type: 'category' | 'value' | 'time' | 'log'
    data?: string[]
    name?: string
    axisLabel?: any
    axisLine?: any
    axisTick?: any
  }
  yAxis?: {
    type: 'category' | 'value' | 'time' | 'log'
    name?: string
    axisLabel?: any
    axisLine?: any
    axisTick?: any
    splitLine?: any
  }
  series: ChartSeries[]
  tooltip?: {
    show: boolean
    trigger?: 'item' | 'axis'
    formatter?: string | Function
    backgroundColor?: string
    borderColor?: string
    textStyle?: any
  }
  color?: string[]
  backgroundColor?: string
  animation?: boolean
  animationDuration?: number
}

// 图表选项类型
export interface ChartOptions {
  responsive: boolean
  maintainAspectRatio: boolean
  animation: boolean
  theme: 'light' | 'dark' | 'custom'
  locale: string
  renderer: 'canvas' | 'svg'
  devicePixelRatio?: number
}

// 图表主题类型
export interface ChartTheme {
  name: string
  backgroundColor: string
  textStyle: {
    color: string
    fontFamily: string
    fontSize: number
  }
  color: string[]
  categoryAxis: {
    axisLine: { lineStyle: { color: string } }
    axisTick: { lineStyle: { color: string } }
    axisLabel: { color: string }
    splitLine: { lineStyle: { color: string } }
  }
  valueAxis: {
    axisLine: { lineStyle: { color: string } }
    axisTick: { lineStyle: { color: string } }
    axisLabel: { color: string }
    splitLine: { lineStyle: { color: string } }
  }
  legend: {
    textStyle: { color: string }
  }
  tooltip: {
    backgroundColor: string
    borderColor: string
    textStyle: { color: string }
  }
}

// 图表类型枚举
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  SCATTER = 'scatter',
  MAP = 'map',
  GAUGE = 'gauge',
  RADAR = 'radar',
  FUNNEL = 'funnel',
  SANKEY = 'sankey',
  TREEMAP = 'treemap'
}

// 图表尺寸类型
export interface ChartSize {
  width: number | string
  height: number | string
}

// 图表事件类型
export interface ChartEvent {
  type: string
  data: any
  target: any
}

// 图表实例类型
export interface ChartInstance {
  id: string
  type: ChartType
  config: ChartConfig
  options: ChartOptions
  element: HTMLElement
  echarts: any
  resize: () => void
  dispose: () => void
  setOption: (option: ChartConfig, notMerge?: boolean) => void
  getOption: () => ChartConfig
  on: (eventName: string, handler: (event: ChartEvent) => void) => void
  off: (eventName: string, handler?: (event: ChartEvent) => void) => void
}

// 图表容器属性
export interface ChartContainerProps {
  id?: string
  type: ChartType
  data: ChartDataItem[]
  config?: Partial<ChartConfig>
  options?: Partial<ChartOptions>
  size?: ChartSize
  loading?: boolean
  empty?: boolean
  error?: string
  onReady?: (instance: ChartInstance) => void
  onError?: (error: Error) => void
  onDataZoom?: (params: any) => void
  onClick?: (params: any) => void
}

// 图表数据源类型
export interface ChartDataSource {
  url?: string
  method?: 'GET' | 'POST'
  params?: Record<string, any>
  transform?: (data: any) => ChartDataItem[]
  polling?: {
    enabled: boolean
    interval: number
  }
}

// 图表导出配置
export interface ChartExportConfig {
  type: 'png' | 'jpg' | 'svg' | 'pdf'
  pixelRatio: number
  backgroundColor: string
  excludeComponents?: string[]
}
