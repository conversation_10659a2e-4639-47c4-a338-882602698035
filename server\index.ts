import express from 'express'
import cors from 'cors'

const app = express()
const PORT = process.env.PORT || 8080

// 模拟数据
const mockProcessingData = [
  { 
    value: 1000, 
    enterpriseType: '制造业', 
    product: '电子', 
    taxExemption: 50, 
    date: '2023-01-15' 
  },
  { 
    value: 1500, 
    enterpriseType: '高新技术', 
    product: '机械', 
    taxExemption: 75, 
    date: '2023-02-20' 
  }
]

const mockTransportData = [
  { 
    value: 800, 
    enterpriseType: '物流', 
    product: '汽车', 
    taxExemption: 30, 
    date: '2023-03-10' 
  },
  { 
    value: 1200, 
    enterpriseType: '运输', 
    product: '货车', 
    taxExemption: 45, 
    date: '2023-04-05' 
  }
]

app.use(cors())
app.use(express.json())

app.get('/api/processing-data', (req, res) => {
  res.json(mockProcessingData)
})

app.get('/api/transport-data', (req, res) => {
  res.json(mockTransportData)
})

app.listen(PORT, () => {
  console.log(`服务运行在 http://localhost:${PORT}`)
})

export default app 