<template>
  <div class="transport-dashboard">
    <!-- 顶部标题区 -->
    <header class="dashboard-header">
      <div class="header-content">
        <h1 class="dashboard-title">
          <span class="title-icon">🚗</span>
          交通运输工具监管视窗
        </h1>
        <div class="header-info">
          <div class="update-time">
            数据更新时间：{{ updateTime }}
          </div>
          <div class="status-indicator">
            <span class="status-dot" :class="{ 'online': isOnline }"></span>
            {{ isOnline ? '实时监控' : '离线模式' }}
          </div>
        </div>
      </div>
    </header>

    <!-- 关键指标卡片区 -->
    <section class="indicators-section">
      <div class="indicators-grid">
        <StatCard
          title="零关税车辆"
          :value="transportStats.totalCars"
          unit="辆"
          trend="up"
          :change="15.2"
        />
        <StatCard
          title="免税交通工具"
          :value="transportStats.totalVehicles"
          unit="辆"
          trend="up"
          :change="8.7"
        />
        <StatCard
          title="零配件维修"
          :value="transportStats.repairCount"
          unit="次"
          trend="up"
          :change="12.3"
        />
        <StatCard
          title="维修费用"
          :value="transportStats.repairCost"
          unit="万元"
          trend="up"
          :change="6.8"
        />
      </div>
    </section>

    <!-- 主要图表展示区 -->
    <section class="charts-section">
      <div class="charts-grid">
        <!-- 零关税车辆品牌分布 -->
        <div class="chart-card">
          <CustomsChart
            type="pie"
            :data="carBrandsData"
            title="零关税车辆品牌分布"
            subtitle="按品牌统计"
            data-format="vehicle"
            :show-legend="true"
            :height="280"
          />
        </div>

        <!-- 免税交通工具类型 -->
        <div class="chart-card">
          <CustomsChart
            type="doughnut"
            :data="vehicleTypesData"
            title="免税交通工具类型"
            subtitle="按类型分类"
            data-format="vehicle"
            :show-legend="true"
            :height="280"
          />
        </div>

        <!-- 月度进口趋势 -->
        <div class="chart-card chart-card-wide">
          <CustomsChart
            type="line"
            :data="monthlyTrendData"
            title="交通工具月度进口趋势"
            subtitle="零关税政策实施效果"
            data-format="vehicle"
            :config="{ smooth: true, showArea: true }"
            :height="280"
          />
        </div>

        <!-- 企业排行榜 -->
        <div class="chart-card">
          <CustomsChart
            type="bar"
            :data="enterpriseRankingData"
            title="运输企业排行榜"
            subtitle="按减免税费排名"
            data-format="currency"
            :config="{ direction: 'horizontal', showLabel: true }"
            :height="280"
          />
        </div>
      </div>
    </section>

    <!-- 数据加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载交通运输数据...</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { transportDataService } from '../shared/services/dataService.js'
import { formatDate } from '../shared/utils/format.js'
import StatCard from '../components/StatCard.vue'
import { CustomsChart } from '../components/charts/index.js'

// 响应式数据
const loading = ref(true)
const isOnline = ref(true)
const updateTime = ref('')
const transportData = ref({})

// 定时器
let updateTimer = null

// 计算属性
const transportStats = computed(() => ({
  totalCars: transportData.value.zeroTariffCars?.totalCount || 0,
  totalVehicles: transportData.value.dutyFreeVehicles?.totalCount || 0,
  repairCount: transportData.value.sparePartsData?.repairCount || 0,
  repairCost: transportData.value.sparePartsData?.totalCost || 0
}))

const carBrandsData = computed(() => {
  return transportData.value.zeroTariffCars?.brands || []
})

const vehicleTypesData = computed(() => {
  return transportData.value.dutyFreeVehicles?.categories || []
})

const monthlyTrendData = computed(() => {
  return transportData.value.zeroTariffCars?.monthlyTrend || []
})

const enterpriseRankingData = computed(() => {
  return transportData.value.enterpriseRanking || []
})

// 方法
const loadData = async () => {
  try {
    loading.value = true

    const [zeroTariffCars, dutyFreeVehicles, sparePartsData, enterpriseRanking] = await Promise.all([
      transportDataService.getZeroTariffCars(),
      transportDataService.getDutyFreeVehicles(),
      transportDataService.getSparePartsData(),
      transportDataService.getEnterpriseRanking()
    ])

    transportData.value = {
      zeroTariffCars,
      dutyFreeVehicles,
      sparePartsData,
      enterpriseRanking
    }

    updateTime.value = formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')

  } catch (error) {
    console.error('加载交通运输数据失败:', error)
    isOnline.value = false
  } finally {
    loading.value = false
  }
}

const startAutoUpdate = () => {
  updateTimer = setInterval(async () => {
    try {
      await loadData()
      isOnline.value = true
    } catch (error) {
      console.warn('自动更新失败:', error)
      isOnline.value = false
    }
  }, 30000)
}

const stopAutoUpdate = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 生命周期
onMounted(async () => {
  await loadData()
  startAutoUpdate()
})

onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<style scoped>
.transport-dashboard {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
  color: #ffffff;
  overflow-y: auto;
  position: relative;
}

.transport-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 复用ProcessingView的样式 */
.dashboard-header {
  height: 80px;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 10;
}

.header-content {
  height: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.header-info {
  display: flex;
  align-items: center;
  gap: 24px;
  font-size: 14px;
}

.update-time {
  color: rgba(255, 255, 255, 0.7);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #f5222d;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background: #52c41a;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.indicators-section {
  padding: 24px;
  position: relative;
  z-index: 1;
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 1920px;
  margin: 0 auto;
}

.charts-section {
  padding: 0 24px 24px;
  position: relative;
  z-index: 1;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1920px;
  margin: 0 auto;
}

.chart-card {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.4);
}

.chart-card-wide {
  grid-column: span 2;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 14, 39, 0.9);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #ffffff;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(59, 130, 246, 0.2);
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .charts-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-card-wide {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    height: 60px;
  }

  .header-content {
    padding: 0 16px;
  }

  .dashboard-title {
    font-size: 18px;
  }

  .header-info {
    gap: 12px;
    font-size: 12px;
  }

  .indicators-section,
  .charts-section {
    padding: 16px;
  }

  .indicators-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-card-wide {
    grid-column: span 1;
  }

  .chart-card {
    padding: 16px;
  }
}

/* 滚动条样式 */
.transport-dashboard::-webkit-scrollbar {
  width: 6px;
}

.transport-dashboard::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

.transport-dashboard::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.transport-dashboard::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}
</style>