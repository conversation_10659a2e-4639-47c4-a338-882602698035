<template>
  <div class="transport-view">
    <TransportChart :data="chartData" />
  </div>
</template>
<script>
import { ref, onMounted } from 'vue'
import { useTransportData } from '../middleware/transport'
import TransportChart from '../components/TransportChart.vue'

export default {
  name: 'TransportView',
  components: { TransportChart },
  setup() {
    const chartData = ref({})
    onMounted(async () => {
      chartData.value = await useTransportData()
    })
    return { chartData }
  }
}
</script>
<style scoped>
.transport-view { width: 100vw; height: 100vh; }
</style> 