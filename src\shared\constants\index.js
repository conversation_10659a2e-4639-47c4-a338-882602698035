/**
 * 海南海关领导视窗系统 - 全局常量定义
 */

// API配置
export const API_CONFIG = {
  BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://api.customs.hainan.gov.cn' 
    : 'http://localhost:3001',
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000
}

// 业务模块常量
export const BUSINESS_MODULES = {
  PROCESSING: 'processing',
  TRANSPORT: 'transport',
  ANALYTICS: 'analytics'
}

// 企业类型
export const ENTERPRISE_TYPES = {
  PROCESSING: '加工增值企业',
  TRADING: '贸易企业',
  LOGISTICS: '物流企业',
  MANUFACTURING: '制造企业',
  SERVICE: '服务企业'
}

// 海南地区代码映射
export const REGION_CODES = {
  HAIKOU: { code: '460100', name: '海口市' },
  SANYA: { code: '460200', name: '三亚市' },
  SANSHA: { code: '460300', name: '三沙市' },
  DANZHOU: { code: '460400', name: '儋州市' },
  WUZHISHAN: { code: '469001', name: '五指山市' },
  QIONGHAI: { code: '469002', name: '琼海市' },
  WANNING: { code: '469006', name: '万宁市' },
  DONGFANG: { code: '469007', name: '东方市' },
  WENCHANG: { code: '469005', name: '文昌市' },
  LINGSHUI: { code: '469028', name: '陵水县' },
  LEDONG: { code: '469027', name: '乐东县' }
}

// 图表主题
export const CHART_THEMES = {
  DARK: 'dark',
  LIGHT: 'light',
  CUSTOM: 'custom'
}

// 图表类型
export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  DOUGHNUT: 'doughnut',
  SCATTER: 'scatter',
  MAP: 'map',
  GAUGE: 'gauge',
  RADAR: 'radar'
}

// 数据更新间隔（毫秒）
export const REFRESH_INTERVALS = {
  REAL_TIME: 1000,
  FAST: 5000,
  NORMAL: 30000,
  SLOW: 60000,
  VERY_SLOW: 300000
}

// 海关业务数据状态
export const DATA_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  EMPTY: 'empty'
}

// 布局类型
export const LAYOUT_TYPES = {
  GRID: 'grid',
  HORIZONTAL: 'horizontal',
  VERTICAL: 'vertical',
  WALL: 'wall',
  DASHBOARD: 'dashboard'
}

// 设备类型
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile'
}

// 海关业务图表颜色配置
export const CHART_COLORS = {
  PRIMARY: ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
  CUSTOMS: ['#2f54eb', '#13c2c2', '#52c41a', '#faad14', '#fa541c', '#eb2f96'],
  GRADIENT: [
    'linear-gradient(90deg, #1890ff 0%, #36cfc9 100%)',
    'linear-gradient(90deg, #13c2c2 0%, #52c41a 100%)',
    'linear-gradient(90deg, #faad14 0%, #fa541c 100%)'
  ]
}

// 动画配置
export const ANIMATION_CONFIG = {
  DURATION: 1000,
  EASING: 'cubicOut',
  DELAY: 0,
  DURATION_UPDATE: 300
}

// 响应式断点
export const BREAKPOINTS = {
  XS: 480,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1600
}

// 海关业务指标类型
export const INDICATOR_TYPES = {
  GOODS_VALUE: 'goods_value',
  TAX_EXEMPTION: 'tax_exemption',
  ENTERPRISE_COUNT: 'enterprise_count',
  ERP_COUNT: 'erp_count',
  VEHICLE_COUNT: 'vehicle_count',
  DECLARATION_COUNT: 'declaration_count'
}

// 交通运输工具类型
export const VEHICLE_TYPES = {
  CAR: '小汽车',
  TRUCK: '货车',
  BUS: '客车',
  MOTORCYCLE: '摩托车',
  ELECTRIC: '电动车',
  YACHT: '游艇',
  SHIP: '船舶'
}

// 产品类别
export const PRODUCT_CATEGORIES = {
  ELECTRONICS: '电子产品',
  TEXTILES: '纺织品',
  FOOD: '食品加工',
  CHEMICAL: '化工产品',
  MACHINERY: '机械设备',
  MEDICAL: '医疗器械',
  AUTO_PARTS: '汽车配件',
  BUILDING: '建材产品',
  AGRICULTURE: '农产品',
  SEAFOOD: '海产品'
}
