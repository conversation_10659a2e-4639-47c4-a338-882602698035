import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDeviceAdapter } from './useDeviceAdapter'
import { useGlobalState } from './useGlobalState'
import { deviceRouteMap, routeConfig } from './routeConfig'

/**
 * 路由守卫服务
 * @returns {object} 路由守卫对象
 */
export const useRouteGuard = () => {
  const router = useRouter()
  const route = useRoute()
  const { deviceType } = useDeviceAdapter()
  const { state } = useGlobalState()

  // 当前模块
  const currentModule = computed(() => {
    const path = route.path.split('/')[1]
    return deviceRouteMap[path] ? path : null
  })

  // 获取设备特定路由
  const getDeviceRoute = (module) => {
    const moduleRoutes = deviceRouteMap[module]
    if (!moduleRoutes) return null

    const device = route.query.device || deviceType.value
    return moduleRoutes[device]
  }

  // 路由前置守卫
  const beforeEach = async (to, from, next) => {
    try {
      // 检查是否需要登录验证
      if (to.matched.some(record => record.meta.requiresAuth !== false)) {
        if (!state.isAuthenticated) {
          return next({
            path: routeConfig.loginRoute,
            query: { redirect: to.fullPath }
          })
        }
      }

      // 如果已登录，禁止访问登录页
      if (to.path === routeConfig.loginRoute && state.isAuthenticated) {
        return next({ path: routeConfig.defaultRoute })
      }

      const module = to.path.split('/')[1]
      const queryDevice = to.query.device

      // 检查设备类型
      if (queryDevice && !routeConfig.supportedDevices.includes(queryDevice)) {
        return next({
          path: routeConfig.errorRoute,
          query: { type: routeConfig.errorTypes.invalidDevice }
        })
      }

      // 处理设备特定路由
      if (deviceRouteMap[module]) {
        const deviceRoute = getDeviceRoute(module)
        if (!deviceRoute) {
          return next({
            path: routeConfig.errorRoute,
            query: { type: routeConfig.errorTypes.notFound }
          })
        }
      }

      next()
    } catch (error) {
      next({
        path: routeConfig.errorRoute,
        query: { type: routeConfig.errorTypes.serverError }
      })
    }
  }

  // 路由后置守卫
  const afterEach = (to) => {
    // 设置页面标题
    const title = to.meta.title
      ? `${to.meta.title}${routeConfig.title.separator}${routeConfig.title.suffix}`
      : routeConfig.title.suffix
    document.title = title
  }

  // 跳转到设备特定页面
  const navigateToDeviceView = (module, device = deviceType.value) => {
    if (!deviceRouteMap[module]) {
      console.error('无效的模块名称')
      return
    }

    router.push({
      path: `/${module}`,
      query: { device }
    })
  }

  return {
    currentModule,
    beforeEach,
    afterEach,
    navigateToDeviceView
  }
}