<template>
  <div class="trend-indicator inline-flex items-center gap-1 text-sm" :class="indicatorType">
    <!-- 趋势图标 -->
    <div class="trend-icon w-4 h-4">
      <svg v-if="indicatorType === 'up'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
        />
      </svg>
      <svg v-else-if="indicatorType === 'down'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6"
        />
      </svg>
      <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M20 12H4"
        />
      </svg>
    </div>

    <!-- 趋势数值 -->
    <span class="font-medium">{{ formattedValue }}</span>

    <!-- 趋势描述 -->
    <span class="text-secondary" v-if="description">{{ description }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useFormatter } from '@/composables/useFormatter'

const props = defineProps({
  value: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    default: 'auto',
    validator: (value) => ['up', 'down', 'flat', 'auto'].includes(value)
  },
  description: {
    type: String,
    default: ''
  },
  format: {
    type: String,
    default: 'percent',
    validator: (value) => ['number', 'currency', 'percent'].includes(value)
  }
})

// 格式化数值
const { formatNumber, formatCurrency, formatPercent } = useFormatter()

const formattedValue = computed(() => {
  switch (props.format) {
    case 'currency':
      return formatCurrency(props.value)
    case 'percent':
      return formatPercent(props.value)
    default:
      return formatNumber(props.value)
  }
})

// 趋势类型
const indicatorType = computed(() => {
  if (props.type === 'auto') {
    if (props.value > 0) return 'up'
    if (props.value < 0) return 'down'
    return 'flat'
  }
  return props.type
})
</script>

<style scoped>
.trend-indicator.up {
  color: var(--success-color);
}

.trend-indicator.down {
  color: var(--error-color);
}

.trend-indicator.flat {
  color: var(--text-secondary);
}

.trend-icon svg {
  width: 100%;
  height: 100%;
}
</style>