<template>
  <div class="mobile-dashboard">
    <!-- 移动端头部 - 按示例图设计 -->
    <header class="mobile-header">
      <div class="header-top">
        <button class="back-btn">‹</button>
        <h1>{{ currentModule.title }}</h1>
      </div>

      <!-- 进度指示器 -->
      <div class="progress-indicator">
        <div class="progress-text">{{ currentModule.title }}达到80%的消费关税</div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: '80%' }"></div>
        </div>
      </div>

      <!-- Tab导航 - 按示例图的4个Tab -->
      <div class="nav-tabs">
        <button
          :class="['tab-btn', { active: activeSubTab === 'overview' }]"
          @click="activeSubTab = 'overview'"
        >
          总体
        </button>
        <button
          :class="['tab-btn', { active: activeSubTab === 'production' }]"
          @click="activeSubTab = 'production'"
        >
          生产加工
        </button>
        <button
          :class="['tab-btn', { active: activeSubTab === 'domestic' }]"
          @click="activeSubTab = 'domestic'"
        >
          内销
        </button>
        <button
          :class="['tab-btn', { active: activeSubTab === 'erp' }]"
          @click="activeSubTab = 'erp'"
        >
          ERP联网
        </button>
      </div>
    </header>

    <!-- 移动端内容区域 - 按示例图重新设计 -->
    <main class="mobile-main">
      <!-- 根据当前Tab显示不同内容 -->
      <div class="tab-content">
        <!-- 总体Tab -->
        <div v-show="activeSubTab === 'overview'" class="tab-panel">
          <div class="chart-container">
            <ChartCard
              title="ERP联网企业数量"
              :data="getERPData()"
              chart-type="bar"
              :height="200"
            />
          </div>
          <div class="chart-container">
            <ChartCard
              title="ERP联网数量TOP5关区"
              :data="getERPRankingData()"
              chart-type="horizontal-bar"
              :height="200"
            />
          </div>
        </div>

        <!-- 生产加工Tab -->
        <div v-show="activeSubTab === 'production'" class="tab-panel">
          <div class="stats-row">
            <div class="stat-item">
              <div class="stat-value">1,234</div>
              <div class="stat-label">生产企业数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">5,678</div>
              <div class="stat-label">加工产值</div>
            </div>
          </div>
          <div class="chart-container">
            <ChartCard
              title="生产加工趋势"
              :data="getProductionTrendData()"
              chart-type="line"
              :height="200"
            />
          </div>
        </div>

        <!-- 内销Tab -->
        <div v-show="activeSubTab === 'domestic'" class="tab-panel">
          <div class="chart-container">
            <ChartCard
              title="内销统计"
              :data="getDomesticSalesData()"
              chart-type="pie"
              :height="200"
            />
          </div>
        </div>

        <!-- ERP联网Tab -->
        <div v-show="activeSubTab === 'erp'" class="tab-panel">
          <div class="chart-container">
            <ChartCard
              title="ERP联网企业分布"
              :data="getERPDistributionData()"
              chart-type="bar"
              :height="200"
            />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { customsDataProcessor } from '@/middleware/dataProcessor'
import StatCard from '@/components/StatCard.vue'
import ChartCard from '@/components/ChartCard.vue'

export default {
  name: 'MobileDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const activeSubTab = ref('overview')
    const loading = ref(false)
    const error = ref('')
    const dashboardData = ref(null)

    // 当前模块（默认加工增值）
    const currentModule = ref({
      title: '加工增值',
      id: 'processing'
    })

    // 加载数据
    const loadData = async () => {
      loading.value = true
      error.value = ''

      try {
        const data = await customsDataProcessor.getProcessingData('mobile')
        dashboardData.value = data
      } catch (err) {
        error.value = err.message || '数据加载失败'
        console.error('移动端数据加载失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 刷新数据
    const refreshData = () => {
      customsDataProcessor.clearCache()
      loadData()
    }

    // 模拟数据方法（保持向后兼容）
    const getERPData = () => ({
      categories: ['一月', '二月', '三月', '四月', '五月', '六月'],
      series: [{
        name: '数量',
        data: [8, 10, 12, 15, 18, 22]
      }]
    })

    const getERPRankingData = () => ({
      categories: ['黄埔海关', '洋浦港区', '海口港区', '三亚海关', '马村海关'],
      series: [{
        name: '数量',
        data: [18, 15, 12, 8, 6]
      }]
    })

    const getProductionTrendData = () => ({
      categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
      series: [{
        name: '产值',
        data: [120, 132, 101, 134, 90, 230]
      }]
    })

    const getDomesticSalesData = () => ({
      series: [
        { name: '海口', value: 335 },
        { name: '三亚', value: 310 },
        { name: '儋州', value: 234 },
        { name: '琼海', value: 135 }
      ]
    })

    const getERPDistributionData = () => ({
      categories: ['区内', '区外'],
      series: [{
        name: '企业数',
        data: [25, 15]
      }]
    })

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      activeSubTab,
      currentModule,
      loading,
      error,
      dashboardData,
      refreshData,
      getERPData,
      getERPRankingData,
      getProductionTrendData,
      getDomesticSalesData,
      getERPDistributionData
    }
  }
}
</script>

<style scoped>
.mobile-dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  color: #fff;
}

.mobile-header {
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid #00d4ff;
}

.header-top {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.back-btn {
  background: none;
  border: none;
  color: #00d4ff;
  font-size: 1.5rem;
  margin-right: 15px;
  cursor: pointer;
}

.header-top h1 {
  font-size: 1.5rem;
  color: #fff;
  margin: 0;
  flex: 1;
}

.progress-indicator {
  margin-bottom: 15px;
}

.progress-text {
  font-size: 0.9rem;
  color: #00d4ff;
  margin-bottom: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #0099cc);
  transition: width 0.3s ease;
}

.nav-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  overflow: hidden;
}

.tab-btn {
  flex: 1;
  padding: 12px 8px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn:hover {
  background: rgba(0, 212, 255, 0.1);
  color: #fff;
}

.tab-btn.active {
  background: rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  font-weight: bold;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #00d4ff;
}

.mobile-main {
  padding: 15px;
}

.tab-content {
  min-height: calc(100vh - 200px);
}

.tab-panel {
  display: block;
}

.chart-container {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.stats-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.stat-item {
  flex: 1;
  background: rgba(0, 0, 0, 0.4);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.mobile-section {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-grid-mobile {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.charts-mobile {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
