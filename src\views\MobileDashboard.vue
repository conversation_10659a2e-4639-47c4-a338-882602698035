<template>
  <div class="mobile-dashboard">
    <!-- 移动端头部 -->
    <header class="mobile-header">
      <div class="header-content">
        <h1>{{ pageConfig.title }}</h1>
        <div class="device-badge">📱 移动端</div>
      </div>
      <div class="nav-tabs">
        <button
          v-for="module in pageConfig.modules"
          :key="module.id"
          :class="['tab-btn', { active: activeTab === module.id }]"
          @click="activeTab = module.id"
        >
          {{ module.title }}
        </button>
      </div>
    </header>

    <!-- 移动端内容区域 -->
    <main class="mobile-main">
      <!-- 动态模块渲染 -->
      <section
        v-for="module in pageConfig.modules"
        :key="module.id"
        v-show="activeTab === module.id"
        class="mobile-section"
      >
        <!-- 统计卡片 - 2列布局 -->
        <div class="stats-grid-mobile">
          <StatCard
            v-for="stat in module.stats"
            :key="stat.id"
            :title="stat.title"
            :value="stat.getValue()"
            :unit="stat.unit"
            :icon="stat.icon"
          />
        </div>

        <!-- 图表区域 - 单列布局 -->
        <div class="charts-mobile">
          <ChartCard
            v-for="chart in module.charts"
            :key="chart.id"
            :title="chart.title"
            :data="chart.getData()"
            :chart-type="chart.type"
            :height="250"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import ChartCard from '../components/ChartCard.vue'
import StatCard from '../components/StatCard.vue'
import { useDashboardContent } from '../composables/useDashboardContent'

export default {
  name: 'MobileDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const activeTab = ref('processing')
    const { pageConfig, loadAllData } = useDashboardContent()

    onMounted(() => {
      loadAllData()
    })

    return {
      activeTab,
      pageConfig
    }
  }
}
</script>

<style scoped>
.mobile-dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  color: #fff;
}

.mobile-header {
  position: sticky;
  top: 0;
  background: rgba(10, 14, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid #00d4ff;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.header-content h1 {
  font-size: 1.5rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.device-badge {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 15px;
  padding: 5px 12px;
  font-size: 0.8rem;
}

.nav-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.3);
}

.tab-btn {
  flex: 1;
  padding: 12px;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn.active {
  background: rgba(0, 212, 255, 0.2);
  border-bottom-color: #00d4ff;
  color: #00d4ff;
}

.mobile-main {
  padding: 20px;
}

.mobile-section {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-grid-mobile {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.charts-mobile {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
