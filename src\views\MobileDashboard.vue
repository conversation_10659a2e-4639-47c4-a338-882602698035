<template>
  <div class="mobile-dashboard">
    <!-- 移动端头部 -->
    <header class="mobile-header">
      <div class="header-content">
        <h1>海南海关领导视窗</h1>
        <div class="device-badge">📱 移动端</div>
      </div>
      <div class="nav-tabs">
        <button 
          :class="['tab-btn', { active: activeTab === 'processing' }]"
          @click="activeTab = 'processing'"
        >
          加工增值
        </button>
        <button 
          :class="['tab-btn', { active: activeTab === 'transport' }]"
          @click="activeTab = 'transport'"
        >
          交通运输工具
        </button>
      </div>
    </header>

    <!-- 移动端内容区域 -->
    <main class="mobile-main">
      <!-- 加工增值模块 -->
      <section v-show="activeTab === 'processing'" class="mobile-section">
        <!-- 统计卡片 - 2列布局 -->
        <div class="stats-grid-mobile">
          <StatCard 
            title="货物总值"
            :value="processingData.cargoStats?.totalValue"
            unit="万元"
            icon="📦"
          />
          <StatCard 
            title="免征税款"
            :value="processingData.taxExemption?.totalAmount"
            unit="万元"
            icon="💰"
          />
          <StatCard 
            title="备案企业"
            :value="processingData.registeredCompanies?.total"
            unit="家"
            icon="🏢"
          />
          <StatCard 
            title="ERP企业"
            :value="processingData.erpCompanies?.total"
            unit="家"
            icon="💻"
          />
        </div>

        <!-- 图表区域 - 单列布局 -->
        <div class="charts-mobile">
          <ChartCard 
            title="货物总值月度趋势"
            :data="processingData.cargoStats?.monthlyTrend"
            chart-type="line"
            :height="250"
          />
          <ChartCard 
            title="免征税款地区分布"
            :data="processingData.taxExemption?.byRegion"
            chart-type="horizontal-bar"
            :height="250"
          />
          <ChartCard 
            title="备案企业类型分布"
            :data="processingData.registeredCompanies?.byType"
            chart-type="pie"
            :height="250"
          />
          <ChartCard 
            title="ERP联网企业趋势"
            :data="processingData.erpCompanies?.monthlyTrend"
            chart-type="line"
            :height="250"
          />
        </div>
      </section>

      <!-- 交通运输工具模块 -->
      <section v-show="activeTab === 'transport'" class="mobile-section">
        <!-- 统计卡片 -->
        <div class="stats-grid-mobile">
          <StatCard 
            title="零关税车辆"
            :value="transportData.zeroDutyVehicles?.total"
            unit="辆"
            icon="🚗"
          />
          <StatCard 
            title="免税车辆"
            :value="transportData.dutyFreeVehicles?.total"
            unit="辆"
            icon="🚙"
          />
          <StatCard 
            title="维修车辆"
            :value="transportData.mainlandVehicleRepair?.total"
            unit="辆"
            icon="🔧"
          />
        </div>

        <!-- 图表区域 -->
        <div class="charts-mobile">
          <ChartCard 
            title="零关税车辆月度趋势"
            :data="transportData.zeroDutyVehicles?.monthlyTrend"
            chart-type="line"
            :height="250"
          />
          <ChartCard 
            title="零关税车辆类型分布"
            :data="transportData.zeroDutyVehicles?.byType"
            chart-type="pie"
            :height="250"
          />
          <ChartCard 
            title="免税车辆类别分布"
            :data="transportData.dutyFreeVehicles?.byCategory"
            chart-type="pie"
            :height="250"
          />
          <ChartCard 
            title="交通工具减免税费企业排行"
            :data="transportData.taxReductionRanking"
            chart-type="horizontal-bar"
            :height="250"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref } from 'vue'
import ChartCard from '../components/ChartCard.vue'
import StatCard from '../components/StatCard.vue'
import { useProcessingData } from '../composables/useProcessingData'
import { useTransportData } from '../composables/useTransportData'

export default {
  name: 'MobileDashboard',
  components: {
    ChartCard,
    StatCard
  },
  setup() {
    const activeTab = ref('processing')
    const { processingData } = useProcessingData()
    const { transportData } = useTransportData()

    return {
      activeTab,
      processingData,
      transportData
    }
  }
}
</script>

<style scoped>
.mobile-dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  color: #fff;
}

.mobile-header {
  position: sticky;
  top: 0;
  background: rgba(10, 14, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid #00d4ff;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.header-content h1 {
  font-size: 1.5rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.device-badge {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 15px;
  padding: 5px 12px;
  font-size: 0.8rem;
}

.nav-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.3);
}

.tab-btn {
  flex: 1;
  padding: 12px;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn.active {
  background: rgba(0, 212, 255, 0.2);
  border-bottom-color: #00d4ff;
  color: #00d4ff;
}

.mobile-main {
  padding: 20px;
}

.mobile-section {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-grid-mobile {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.charts-mobile {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
