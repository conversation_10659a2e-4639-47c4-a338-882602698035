<template>
  <div class="processing-stats" :class="theme">
    <!-- 数据卡片 -->
    <div class="stats-cards">
      <el-card v-for="stat in stats" :key="stat.key" class="stat-card">
        <template #header>
          <div class="card-header">
            <span>{{ stat.title }}</span>
            <el-tag :type="stat.tagType" effect="dark">{{ stat.trend }}</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ stat.value }} {{ stat.unit }}</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="lineChartAdapter.transform(stat.chartData, {
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                smooth: true,
                showSymbol: false,
                lineStyle: { width: 3 },
                areaStyle: { opacity: 0.1 }
              })"
              autoresize
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>产品类型分布</span>
            <el-tag type="info" effect="dark">月度统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="pieChartAdapter.transform(data?.production?.productTypes, {
              radius: ['40%', '70%'],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            })"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>企业规模分布</span>
            <el-tag type="primary" effect="dark">月度统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="barChartAdapter.transform(data?.enterprise?.scaleDistribution?.map(item => item.value), {
              xAxis: {
                type: 'category',
                data: data?.enterprise?.scaleDistribution?.map(item => item.name)
              },
              yAxis: {
                type: 'value',
                name: '家'
              },
              barWidth: '60%',
              label: {
                show: true,
                position: 'top'
              }
            })"
            autoresize
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useThemeStore } from '@/stores'
import { formatNumber } from '@/utils/format'
import { BarChartAdapter, PieChartAdapter, LineChartAdapter } from '@/adapters/charts'

// 属性定义
const props = defineProps({
  loading: Boolean,
  data: Object
})

// 主题状态
const themeStore = useThemeStore()
const theme = computed(() => themeStore.theme)

// 图表适配器
const barChartAdapter = new BarChartAdapter(theme.value)
const pieChartAdapter = new PieChartAdapter(theme.value)
const lineChartAdapter = new LineChartAdapter(theme.value)

// 月份数据
const monthData = ['1月', '2月', '3月', '4月', '5月', '6月']

// 统计卡片数据
const stats = computed(() => [
  {
    key: 'monthly_value',
    title: '本月产值',
    value: formatNumber(props.data?.production?.monthlyValue || 0),
    unit: '万元',
    trend: `同比增长 ${props.data?.production?.yearOverYear || 0}%`,
    tagType: 'success',
    chartData: props.data?.production?.monthlyTrend || []
  },
  {
    key: 'enterprise_count',
    title: '企业总数',
    value: formatNumber(props.data?.enterprise?.total || 0),
    unit: '家',
    trend: `ERP接入率 ${props.data?.enterprise?.erpRate || 0}%`,
    tagType: 'warning',
    chartData: props.data?.enterprise?.growthTrend?.map(item => item.total) || []
  },
  {
    key: 'cargo_value',
    title: '货物总值',
    value: formatNumber(props.data?.cargo?.totalValue || 0),
    unit: '万元',
    trend: `同比增长 ${props.data?.cargo?.valueGrowth || 0}%`,
    tagType: 'danger',
    chartData: props.data?.cargo?.monthlyStats?.map(item => item.value) || []
  }
])
</script>

<style scoped>
.processing-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.stat-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.chart {
  height: 80px;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--grid-gap);
  flex: 1;
}

.chart-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.chart-content {
  height: 100%;
  min-height: 300px;
}
</style>