import { ProcessingModel, TransportModel } from '../models'

// 领域服务基类
class DomainService {
  constructor(api) {
    this.api = api
  }

  async fetchData() {
    throw new Error('Not implemented')
  }

  transformData(data) {
    throw new Error('Not implemented')
  }
}

// 加工增值服务
export class ProcessingService extends DomainService {
  async fetchData() {
    try {
      const response = await this.api.get('/api/data/processing')
      return this.transformData(response.data)
    } catch (error) {
      console.error('获取加工增值数据失败:', error)
      throw error
    }
  }

  transformData(data) {
    return new ProcessingModel({
      cargo: {
        monthlyValue: data.cargoStats?.monthlyValue,
        yearOverYear: data.cargoStats?.yearOverYear,
        monthOverMonth: data.cargoStats?.monthOverMonth,
        types: data.cargoStats?.types || [],
        trends: data.cargoStats?.trends || []
      },
      enterprise: {
        registeredCount: data.enterpriseStats?.registeredCount,
        activeCount: data.enterpriseStats?.activeCount,
        ranking: data.enterpriseStats?.ranking || [],
        erpTotal: data.enterpriseStats?.erpTotal,
        erpConnected: data.enterpriseStats?.erpConnected,
        erpRatio: data.enterpriseStats?.erpRatio
      },
      production: {
        declarationCount: data.productionStats?.declarationCount,
        declarationValue: data.productionStats?.declarationValue,
        declarationTrends: data.productionStats?.declarationTrends || [],
        shipmentCount: data.productionStats?.shipmentCount,
        shipmentValue: data.productionStats?.shipmentValue,
        shipmentTrends: data.productionStats?.shipmentTrends || []
      },
      domestic: {
        monthlyValue: data.domesticStats?.monthlyValue,
        monthlyCount: data.domesticStats?.monthlyCount,
        trends: data.domesticStats?.trends || [],
        distribution: data.domesticStats?.distribution || []
      }
    })
  }
}

// 交通工具服务
export class TransportService extends DomainService {
  async fetchData() {
    try {
      const response = await this.api.get('/api/data/transport')
      return this.transformData(response.data)
    } catch (error) {
      console.error('获取交通工具数据失败:', error)
      throw error
    }
  }

  transformData(data) {
    return new TransportModel({
      zeroTariff: {
        count: data.zeroTariffStats?.count,
        value: data.zeroTariffStats?.value,
        trends: data.zeroTariffStats?.trends || [],
        typeDistribution: data.zeroTariffStats?.typeDistribution || [],
        enterpriseRanking: data.zeroTariffStats?.enterpriseRanking || []
      },
      dutyFree: {
        count: data.dutyFreeStats?.count,
        value: data.dutyFreeStats?.value,
        trends: data.dutyFreeStats?.trends || [],
        typeDistribution: data.dutyFreeStats?.typeDistribution || [],
        enterpriseRanking: data.dutyFreeStats?.enterpriseRanking || []
      },
      maintenance: {
        count: data.maintenanceStats?.count,
        value: data.maintenanceStats?.value,
        trends: data.maintenanceStats?.trends || [],
        typeDistribution: data.maintenanceStats?.typeDistribution || [],
        enterpriseRanking: data.maintenanceStats?.enterpriseRanking || []
      },
      analysis: {
        zeroTariffAnalysis: data.comprehensiveAnalysis?.zeroTariffAnalysis || {},
        equipmentAnalysis: data.comprehensiveAnalysis?.equipmentAnalysis || {},
        vehicleRanking: data.comprehensiveAnalysis?.vehicleRanking || [],
        equipmentRanking: data.comprehensiveAnalysis?.equipmentRanking || []
      }
    })
  }
}