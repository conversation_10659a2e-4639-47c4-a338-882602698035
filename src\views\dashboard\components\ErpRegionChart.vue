<template>
  <ChartCard title="ERP企业TOP5区域" :options="chartOptions" />
</template>

<script setup>
import { computed } from 'vue';
import ChartCard from '../../../components/ChartCard.vue';
import { useThemeStore } from '../../../stores/theme.js';

// 接收父组件传递的数据
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 获取主题状态
const themeStore = useThemeStore();

// 计算图表配置
const chartOptions = computed(() => {
  // 确保数据存在
  if (!props.data || !props.data.topRegions || props.data.topRegions.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    };
  }

  // 提取区域数据并按企业数量排序
  const regions = [...(props.data.topRegions || [])];
  regions.sort((a, b) => b.value - a.value);
  
  // 取前5名区域
  const top5Regions = regions.slice(0, 5);
  
  // 图表配置
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: top5Regions.map(item => item.name),
      inverse: true
    },
    series: [
      {
        name: '企业数量',
        type: 'bar',
        barWidth: '60%',
        data: top5Regions.map(item => ({
          value: item.value,
          itemStyle: {
            color: themeStore.isDark ? '#63e2b7' : '#67C23A'
          }
        })),
        label: {
          show: true,
          position: 'right',
          formatter: '{c}'
        }
      }
    ]
  };
});
</script>