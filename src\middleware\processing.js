/**
 * 加工增值业务中间件
 * 处理业务逻辑和数据转换
 */

import { processingDataService } from '../shared/services/dataService.js'
import { formatLargeNumber, formatPercentage } from '../shared/utils/format.js'

/**
 * 获取加工增值数据（兼容原有接口）
 */
export async function useProcessingData() {
  try {
    const data = await processingDataService.getProcessingData()

    // 数据格式化和业务逻辑处理
    if (data.stats) {
      // 格式化统计数据
      Object.keys(data.stats).forEach(key => {
        if (typeof data.stats[key] === 'number') {
          data.stats[key + '_formatted'] = formatLargeNumber(data.stats[key])
        }
      })
    }

    // 计算增长率
    if (data.valueTrend) {
      ['区内', '区外'].forEach(zone => {
        if (data.valueTrend[zone] && data.valueTrend[zone].length > 1) {
          const trend = data.valueTrend[zone]
          const current = trend[trend.length - 1].value
          const previous = trend[trend.length - 2].value
          const growth = ((current - previous) / previous) * 100

          data.valueTrend[zone + '_growth'] = {
            value: growth,
            formatted: formatPercentage(growth / 100, 1, true)
          }
        }
      })
    }

    return data
  } catch (error) {
    console.error('获取加工增值数据失败:', error)
    return {
      stats: {},
      valueTrend: {},
      companyType: [],
      top5Company: [],
      erpCompanyCount: 0,
      erpTop5Customs: [],
      cityTable: [],
      areaTable: [],
      colorBlocks: [],
      tree: { name: '海南', children: [] }
    }
  }
}

/**
 * 获取关键指标数据
 */
export async function useKeyIndicators() {
  try {
    const [basicStats, taxData, enterpriseStats] = await Promise.all([
      processingDataService.getBasicStats(),
      processingDataService.getTaxExemptionData(),
      processingDataService.getEnterpriseStats()
    ])

    return [
      {
        title: '内销货值',
        value: basicStats.内销货值 || 0,
        unit: '万元',
        trend: 'up',
        change: 12.5
      },
      {
        title: '外贸货值',
        value: basicStats.外贸货值 || 0,
        unit: '万元',
        trend: 'up',
        change: 8.3
      },
      {
        title: '免征税额',
        value: taxData.totalAmount || 0,
        unit: '万元',
        trend: 'up',
        change: 15.2
      },
      {
        title: '备案企业',
        value: enterpriseStats.total || 0,
        unit: '家',
        trend: 'up',
        change: 6.8
      }
    ]
  } catch (error) {
    console.error('获取关键指标失败:', error)
    return []
  }
}