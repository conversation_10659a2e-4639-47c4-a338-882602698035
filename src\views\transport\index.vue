<template>
  <component :is="currentView" />
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

// 视图组件
import PCView from './pc/index.vue'
import MobileView from './mobile/index.vue'
import WallView from './wall/index.vue'

const route = useRoute()

// 根据设备类型动态加载视图组件
const currentView = computed(() => {
  const device = route.query.device || 'pc'
  switch (device) {
    case 'mobile':
      return MobileView
    case 'wall':
      return WallView
    default:
      return PCView
  }
})
</script>