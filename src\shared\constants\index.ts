/**
 * 全局常量定义
 */

// API配置
export const API_CONFIG = {
  BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://api.customs.hainan.gov.cn' 
    : 'http://localhost:3001',
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000
} as const

// 业务模块常量
export const BUSINESS_MODULES = {
  PROCESSING: 'processing',
  TRANSPORT: 'transport',
  ANALYTICS: 'analytics'
} as const

// 企业类型
export const ENTERPRISE_TYPES = {
  PROCESSING: '加工增值企业',
  TRADING: '贸易企业',
  LOGISTICS: '物流企业',
  MANUFACTURING: '制造企业',
  SERVICE: '服务企业'
} as const

// 地区代码映射
export const REGION_CODES = {
  HAIKOU: { code: '460100', name: '海口市' },
  SANYA: { code: '460200', name: '三亚市' },
  SANSHA: { code: '460300', name: '三沙市' },
  DANZHOU: { code: '460400', name: '儋州市' },
  WUZHISHAN: { code: '469001', name: '五指山市' },
  QIONGHAI: { code: '469002', name: '琼海市' },
  WANNING: { code: '469006', name: '万宁市' },
  DONGFANG: { code: '469007', name: '东方市' }
} as const

// 图表主题
export const CHART_THEMES = {
  DARK: 'dark',
  LIGHT: 'light',
  CUSTOM: 'custom'
} as const

// 图表类型
export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  SCATTER: 'scatter',
  MAP: 'map',
  GAUGE: 'gauge',
  RADAR: 'radar'
} as const

// 数据更新间隔（毫秒）
export const REFRESH_INTERVALS = {
  REAL_TIME: 1000,
  FAST: 5000,
  NORMAL: 30000,
  SLOW: 60000,
  VERY_SLOW: 300000
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 1000
} as const

// 导出格式
export const EXPORT_FORMATS = {
  EXCEL: 'excel',
  PDF: 'pdf',
  CSV: 'csv',
  PNG: 'png',
  SVG: 'svg'
} as const

// HTTP状态码
export const STATUS_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const

// 本地存储键名
export const STORAGE_KEYS = {
  USER_TOKEN: 'customs_user_token',
  USER_INFO: 'customs_user_info',
  THEME: 'customs_theme',
  LANGUAGE: 'customs_language',
  LAYOUT_CONFIG: 'customs_layout_config',
  CHART_CONFIG: 'customs_chart_config',
  CACHE_PREFIX: 'customs_cache_'
} as const

// 事件名称
export const EVENT_NAMES = {
  DATA_UPDATED: 'data:updated',
  CHART_RENDERED: 'chart:rendered',
  CHART_CLICKED: 'chart:clicked',
  ERROR_OCCURRED: 'error:occurred',
  USER_ACTION: 'user:action',
  LAYOUT_CHANGED: 'layout:changed',
  THEME_CHANGED: 'theme:changed'
} as const

// 权限级别
export const PERMISSION_LEVELS = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  EXPORT: 'export',
  ADMIN: 'admin'
} as const

// 数据状态
export const DATA_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  EMPTY: 'empty'
} as const

// 布局类型
export const LAYOUT_TYPES = {
  GRID: 'grid',
  HORIZONTAL: 'horizontal',
  VERTICAL: 'vertical',
  WALL: 'wall',
  DASHBOARD: 'dashboard'
} as const

// 设备类型
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile'
} as const

// 图表颜色配置
export const CHART_COLORS = {
  PRIMARY: ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
  BUSINESS: ['#2f54eb', '#13c2c2', '#52c41a', '#faad14', '#fa541c', '#eb2f96'],
  GRADIENT: [
    'linear-gradient(90deg, #1890ff 0%, #36cfc9 100%)',
    'linear-gradient(90deg, #13c2c2 0%, #52c41a 100%)',
    'linear-gradient(90deg, #faad14 0%, #fa541c 100%)'
  ]
} as const

// 动画配置
export const ANIMATION_CONFIG = {
  DURATION: 1000,
  EASING: 'cubicOut',
  DELAY: 0,
  DURATION_UPDATE: 300
} as const

// 响应式断点
export const BREAKPOINTS = {
  XS: 480,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1600
} as const
