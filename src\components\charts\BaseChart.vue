<template>
  <div 
    ref="chartContainer" 
    :class="containerClass"
    :style="containerStyle"
  >
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <span>数据加载中...</span>
    </div>
    <div v-else-if="error" class="chart-error">
      <div class="error-icon">⚠</div>
      <span>{{ error }}</span>
    </div>
    <div v-else-if="isEmpty" class="chart-empty">
      <div class="empty-icon">📊</div>
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

// Props定义
const props = defineProps({
  // 图表配置
  option: {
    type: Object,
    required: true
  },
  // 图表主题
  theme: {
    type: String,
    default: 'dark'
  },
  // 容器尺寸
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '400px'
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 错误信息
  error: {
    type: String,
    default: ''
  },
  // 是否为空
  isEmpty: {
    type: Boolean,
    default: false
  },
  // 是否自动调整大小
  autoResize: {
    type: Boolean,
    default: true
  },
  // 渲染器类型
  renderer: {
    type: String,
    default: 'canvas',
    validator: value => ['canvas', 'svg'].includes(value)
  }
})

// Emits定义
const emit = defineEmits([
  'ready',
  'click',
  'dblclick',
  'mousedown',
  'mousemove',
  'mouseup',
  'mouseover',
  'mouseout',
  'globalout',
  'contextmenu'
])

// 响应式数据
const chartContainer = ref(null)
let chartInstance = null
let resizeObserver = null

// 计算属性
const containerClass = computed(() => ({
  'base-chart': true,
  'chart-loading-state': props.loading,
  'chart-error-state': props.error,
  'chart-empty-state': props.isEmpty
}))

const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

// 初始化图表
const initChart = async () => {
  if (!chartContainer.value || props.loading || props.error || props.isEmpty) {
    return
  }

  await nextTick()

  try {
    // 销毁已存在的实例
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }

    // 创建新实例
    chartInstance = echarts.init(
      chartContainer.value,
      props.theme,
      {
        renderer: props.renderer,
        useDirtyRect: true // 启用脏矩形优化
      }
    )

    // 设置图表配置
    chartInstance.setOption(props.option, true)

    // 绑定事件
    bindEvents()

    // 触发ready事件
    emit('ready', chartInstance)

    // 设置自动调整大小
    if (props.autoResize) {
      setupResize()
    }
  } catch (error) {
    console.error('图表初始化失败:', error)
    emit('error', error)
  }
}

// 绑定图表事件
const bindEvents = () => {
  if (!chartInstance) return

  const events = [
    'click', 'dblclick', 'mousedown', 'mousemove', 'mouseup',
    'mouseover', 'mouseout', 'globalout', 'contextmenu'
  ]

  events.forEach(eventName => {
    chartInstance.on(eventName, (params) => {
      emit(eventName, params)
    })
  })
}

// 设置自动调整大小
const setupResize = () => {
  if (!chartContainer.value || !chartInstance) return

  // 使用ResizeObserver监听容器大小变化
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        chartInstance.resize()
      }
    })
    resizeObserver.observe(chartContainer.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表配置
const updateChart = (newOption, notMerge = false) => {
  if (chartInstance) {
    chartInstance.setOption(newOption, notMerge)
  }
}

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }

  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  window.removeEventListener('resize', handleResize)
}

// 监听配置变化
watch(
  () => props.option,
  (newOption) => {
    if (chartInstance && newOption) {
      updateChart(newOption)
    }
  },
  { deep: true }
)

// 监听状态变化
watch(
  [() => props.loading, () => props.error, () => props.isEmpty],
  () => {
    nextTick(() => {
      if (!props.loading && !props.error && !props.isEmpty) {
        initChart()
      } else if (chartInstance) {
        destroyChart()
      }
    })
  }
)

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  destroyChart()
})

// 暴露方法给父组件
defineExpose({
  chartInstance,
  updateChart,
  resize: () => chartInstance?.resize(),
  dispose: destroyChart
})
</script>

<style scoped>
.base-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-loading,
.chart-error,
.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.error-icon,
.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
