/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--primary-color)'
        },
        success: {
          DEFAULT: 'var(--success-color)'
        },
        warning: {
          DEFAULT: 'var(--warning-color)'
        },
        error: {
          DEFAULT: 'var(--error-color)'
        }
      },
      backgroundColor: {
        'nav': 'var(--nav-bg-color)',
        'wall': 'var(--wall-bg-color)'
      },
      textColor: {
        'base': 'var(--text-color)',
        'secondary': 'var(--text-secondary)'
      },
      spacing: {
        '18': '4.5rem',
        '72': '18rem',
        '84': '21rem',
        '96': '24rem'
      }
    }
  },
  plugins: []
}