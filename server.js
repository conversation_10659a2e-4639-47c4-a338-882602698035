const express = require('express');
const path = require('path');
const WebSocket = require('ws');
const fs = require('fs');
const chokidar = require('chokidar');

// 创建Express应用
const app = express();

// 静态文件中间件
app.use(express.static(path.join(__dirname)));

// 所有路由都返回index.html
app.get('*', (req, res) => {
  fs.readFile(path.join(__dirname, 'index.html'), 'utf8', (err, content) => {
    if (err) {
      res.status(500).send('服务器错误');
      return;
    }

    // 注入WebSocket客户端代码
    const wsClientCode = `
      <script>
        // 建立WebSocket连接
        const ws = new WebSocket('ws://localhost:8001');
        
        // 处理热更新消息
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          
          if (data.type === 'style-update') {
            // 更新样式
            updateStyle(data.path, data.content);
          } else if (data.type === 'vue-update') {
            // 更新Vue组件
            updateComponent(data.path);
          } else if (data.type === 'js-update') {
            // 更新JS模块
            updateModule(data.path);
          }
        };

        // 更新样式
        function updateStyle(path, content) {
          const existingStyle = document.querySelector('style[data-path="' + path + '"]');
          if (existingStyle) {
            existingStyle.textContent = content;
          } else {
            const style = document.createElement('style');
            style.setAttribute('data-path', path);
            style.textContent = content;
            document.head.appendChild(style);
          }
        }

        // 更新Vue组件
        function updateComponent(path) {
          import(path + '?t=' + Date.now())
            .then(module => {
              const component = module.default;
              // 获取当前组件实例
              const instance = window.__VUE_APP__;
              if (instance) {
                // 更新组件定义
                Object.assign(instance.type, component);
                // 触发重新渲染
                instance.update();
              }
            })
            .catch(err => console.error('热更新失败:', err));
        }

        // 更新JS模块
        function updateModule(path) {
          import(path + '?t=' + Date.now())
            .then(module => {
              // 清除模块缓存
              const oldModule = window.__modules__[path];
              if (oldModule) {
                // 执行清理函数
                if (typeof oldModule.cleanup === 'function') {
                  oldModule.cleanup();
                }
              }
              // 缓存新模块
              window.__modules__[path] = module;
            })
            .catch(err => console.error('模块更新失败:', err));
        }

        // 初始化模块缓存
        window.__modules__ = {};
      </script>
    `;

    content = content.replace('</body>', `${wsClientCode}</body>`);
    res.send(content);
  });
});

// 创建HTTP服务器
const server = app.listen(3000, () => {
  console.log('开发服务器运行在: http://localhost:3000');
});

// 创建WebSocket服务器
const wss = new WebSocket.Server({ port: 8001 });
const connections = new Set();

wss.on('connection', (ws) => {
  connections.add(ws);
  ws.on('close', () => connections.delete(ws));
});

// 使用chokidar监听文件变化
const watcher = chokidar.watch('./src', {
  ignored: /(^|[\/\\])\../, // 忽略隐藏文件
  persistent: true
});

// 处理文件变化
watcher.on('change', (filepath) => {
  const relativePath = path.relative(__dirname, filepath);
  const ext = path.extname(filepath);

  // 读取文件内容
  fs.readFile(filepath, 'utf8', (err, content) => {
    if (err) {
      console.error('读取文件失败:', err);
      return;
    }

    // 根据文件类型发送不同的更新消息
    let updateMessage;
    if (ext === '.css') {
      updateMessage = {
        type: 'style-update',
        path: relativePath,
        content
      };
    } else if (ext === '.vue') {
      updateMessage = {
        type: 'vue-update',
        path: relativePath
      };
    } else if (ext === '.js') {
      updateMessage = {
        type: 'js-update',
        path: relativePath
      };
    }

    if (updateMessage) {
      connections.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify(updateMessage));
        }
      });
    }
  });
});