const express = require('express');
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');

const app = express();
const PORT = 3000;

// 静态文件服务
app.use(express.static('.'));

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// 启动服务器
const server = app.listen(PORT, () => {
  console.log(`🚀 海关领导视窗开发服务器运行在 http://localhost:${PORT}`);
  console.log('📁 项目根目录:', __dirname);
  console.log('🔄 支持热重载，修改文件后请刷新浏览器');
});

// 文件监听（简单的热重载提示）
const watcher = chokidar.watch(['index.html'], {ignored: /node_modules/});
watcher.on('change', (filePath) => {
  console.log(`📝 文件已更新: ${filePath} - 请刷新浏览器查看更改`);
});
