/**
 * 数值格式化工具
 */

// 数字格式化选项
const numberFormatOptions = {
  minimumFractionDigits: 0,
  maximumFractionDigits: 2
}

// 货币格式化选项
const currencyFormatOptions = {
  style: 'currency',
  currency: 'CNY',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2
}

// 百分比格式化选项
const percentFormatOptions = {
  style: 'percent',
  minimumFractionDigits: 1,
  maximumFractionDigits: 1
}

/**
 * 格式化工具组合式函数
 */
export function useFormatter() {
  // 格式化数字
  const formatNumber = (value) => {
    if (typeof value !== 'number') return value
    return new Intl.NumberFormat('zh-CN', numberFormatOptions).format(value)
  }

  // 格式化货币
  const formatCurrency = (value) => {
    if (typeof value !== 'number') return value
    return new Intl.NumberFormat('zh-CN', currencyFormatOptions).format(value)
  }

  // 格式化百分比
  const formatPercent = (value) => {
    if (typeof value !== 'number') return value
    return new Intl.NumberFormat('zh-CN', percentFormatOptions).format(value)
  }

  // 格式化日期
  const formatDate = (date, options = {}) => {
    if (!date) return ''
    const defaultOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }
    return new Intl.DateTimeFormat('zh-CN', { ...defaultOptions, ...options }).format(new Date(date))
  }

  // 格式化时间
  const formatTime = (date, options = {}) => {
    if (!date) return ''
    const defaultOptions = {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }
    return new Intl.DateTimeFormat('zh-CN', { ...defaultOptions, ...options }).format(new Date(date))
  }

  // 格式化日期时间
  const formatDateTime = (date, options = {}) => {
    if (!date) return ''
    const defaultOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }
    return new Intl.DateTimeFormat('zh-CN', { ...defaultOptions, ...options }).format(new Date(date))
  }

  return {
    formatNumber,
    formatCurrency,
    formatPercent,
    formatDate,
    formatTime,
    formatDateTime
  }
}