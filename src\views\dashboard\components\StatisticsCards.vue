<template>
  <div class="statistics-cards" :class="{'statistics-cards-mobile': deviceStore.isMobile}">
    <!-- 加工增值统计卡片 -->
    <el-card class="stat-card">
      <template #header>
        <div class="card-header">
          <span>加工增值</span>
          <el-tag size="small" type="success">同比增长 5.2%</el-tag>
        </div>
      </template>
      <div class="card-content">
        <div class="card-value">{{ formatValue(processingData.totalValue) }} 万元</div>
        <div class="card-subtitle">货物数量: {{ processingData.totalCount }} 件</div>
      </div>
    </el-card>
    
    <!-- 交通运输工具统计卡片 -->
    <el-card class="stat-card">
      <template #header>
        <div class="card-header">
          <span>交通运输工具</span>
          <el-tag size="small" type="warning">同比增长 3.8%</el-tag>
        </div>
      </template>
      <div class="card-content">
        <div class="card-value">{{ transportData.totalVehicles }} 辆</div>
        <div class="card-subtitle">总价值: {{ formatValue(transportData.totalValue) }} 万元</div>
      </div>
    </el-card>
    
    <!-- 免征税款统计卡片 -->
    <el-card class="stat-card">
      <template #header>
        <div class="card-header">
          <span>免征税款</span>
          <el-tag size="small" type="danger">同比增长 {{ taxExemptionData.yearOverYear }}%</el-tag>
        </div>
      </template>
      <div class="card-content">
        <div class="card-value">{{ formatValue(taxExemptionData.totalExemption) }} 万元</div>
        <div class="card-subtitle">业务量: {{ taxExemptionData.totalCount }} 票</div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useDeviceStore } from '../../../stores/device.js';

// 接收父组件传递的数据
const props = defineProps({
  processingData: {
    type: Object,
    required: true
  },
  transportData: {
    type: Object,
    required: true
  },
  taxExemptionData: {
    type: Object,
    required: true
  }
});

// 获取设备状态
const deviceStore = useDeviceStore();

// 格式化数值
const formatValue = (value) => {
  if (value === undefined || value === null) return '0';
  return value.toLocaleString('zh-CN', { maximumFractionDigits: 2 });
};
</script>

<style scoped>
.statistics-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.statistics-cards-mobile {
  flex-direction: column;
}

.stat-card {
  flex: 1;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--app-card-shadow-hover);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 移动设备适配 */
@media screen and (max-width: 768px) {
  .statistics-cards {
    flex-direction: column;
  }
}
</style>