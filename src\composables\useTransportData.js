import { reactive } from 'vue'
import { mockDataService } from '../services/mockDataService'

export function useTransportData() {
  const transportData = reactive({
    vehicles: {
      categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
      series: [
        {
          name: '进口车辆',
          data: [120, 150, 180, 220, 280, 350]
        }
      ]
    },
    vehicleTypes: {
      data: [
        { name: '轿车', value: 45 },
        { name: 'SUV', value: 30 },
        { name: '货车', value: 15 },
        { name: '客车', value: 10 }
      ]
    },
    taxReduction: {
      categories: ['企业甲', '企业乙', '企业丙', '企业丁', '企业戊'],
      series: [
        {
          name: '减免税费(万元)',
          data: [850, 720, 650, 580, 420]
        }
      ]
    }
  })

  const loadTransportData = async () => {
    try {
      const data = await mockDataService.getTransportData()
      Object.assign(transportData, data)
    } catch (error) {
      console.error('加载交通运输工具数据失败:', error)
    }
  }

  return {
    transportData,
    loadTransportData
  }
}
