<template>
  <div class="customs-chart">
    <div v-if="title" class="chart-title">
      <h3>{{ title }}</h3>
      <div v-if="subtitle" class="chart-subtitle">{{ subtitle }}</div>
    </div>
    
    <BaseChart
      :type="type"
      :data="processedData"
      :config="chartConfig"
      :loading="loading"
      :error="error"
      :width="width"
      :height="height"
      @ready="handleChartReady"
      @click="handleChartClick"
    />
    
    <div v-if="showLegend && legendData.length" class="chart-legend">
      <div
        v-for="(item, index) in legendData"
        :key="index"
        class="legend-item"
        @click="toggleLegendItem(index)"
      >
        <span 
          class="legend-color" 
          :style="{ backgroundColor: item.color }"
        ></span>
        <span class="legend-text">{{ item.name }}</span>
        <span class="legend-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import BaseChart from './BaseChart.vue'
import { formatLargeNumber, formatPercentage } from '../../shared/utils/format.js'
import { CHART_COLORS } from '../../shared/constants/index.js'

// Props定义
const props = defineProps({
  // 图表类型
  type: {
    type: String,
    required: true,
    validator: value => ['bar', 'line', 'pie', 'doughnut', 'radar', 'gauge'].includes(value)
  },
  // 图表数据
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 图表标题
  title: {
    type: String,
    default: ''
  },
  // 图表副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 数据格式化类型
  dataFormat: {
    type: String,
    default: 'number', // number, currency, percentage, count
    validator: value => ['number', 'currency', 'percentage', 'count', 'enterprise', 'vehicle'].includes(value)
  },
  // 图表配置
  config: {
    type: Object,
    default: () => ({})
  },
  // 容器尺寸
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '300px'
  },
  // 状态
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['ready', 'click', 'legendClick'])

// 响应式数据
const chartInstance = ref(null)

// 计算属性
const processedData = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  return props.data.map(item => {
    if (typeof item === 'object') {
      return {
        name: item.name || item.label || item.key || '',
        value: item.value || item.count || item.amount || 0,
        ...item
      }
    }
    return {
      name: String(item),
      value: Number(item) || 0
    }
  })
})

const chartConfig = computed(() => {
  const baseConfig = {
    colors: CHART_COLORS.CUSTOMS,
    showLabel: true,
    ...props.config
  }

  // 根据数据格式化类型设置配置
  switch (props.dataFormat) {
    case 'currency':
      baseConfig.valueFormatter = (value) => formatLargeNumber(value) + '元'
      break
    case 'percentage':
      baseConfig.valueFormatter = (value) => formatPercentage(value / 100, 1)
      break
    case 'count':
      baseConfig.valueFormatter = (value) => formatLargeNumber(value) + '个'
      break
    case 'enterprise':
      baseConfig.valueFormatter = (value) => formatLargeNumber(value) + '家'
      break
    case 'vehicle':
      baseConfig.valueFormatter = (value) => formatLargeNumber(value) + '辆'
      break
    default:
      baseConfig.valueFormatter = (value) => formatLargeNumber(value)
  }

  // 海关业务特定配置
  if (props.type === 'bar') {
    baseConfig.grid = {
      left: '15%',
      right: '10%',
      top: '15%',
      bottom: '15%'
    }
  }

  if (props.type === 'pie' || props.type === 'doughnut') {
    baseConfig.showLegend = false // 使用自定义图例
    baseConfig.label = {
      formatter: '{b}: {d}%'
    }
  }

  return baseConfig
})

const legendData = computed(() => {
  if (!props.showLegend || !processedData.value.length) return []
  
  return processedData.value.map((item, index) => ({
    name: item.name,
    value: formatValueForLegend(item.value),
    color: CHART_COLORS.CUSTOMS[index % CHART_COLORS.CUSTOMS.length],
    visible: true
  }))
})

// 方法
const formatValueForLegend = (value) => {
  switch (props.dataFormat) {
    case 'currency':
      return formatLargeNumber(value) + '元'
    case 'percentage':
      return formatPercentage(value / 100, 1)
    case 'count':
      return formatLargeNumber(value) + '个'
    case 'enterprise':
      return formatLargeNumber(value) + '家'
    case 'vehicle':
      return formatLargeNumber(value) + '辆'
    default:
      return formatLargeNumber(value)
  }
}

const handleChartReady = (instance) => {
  chartInstance.value = instance
  emit('ready', instance)
}

const handleChartClick = (params) => {
  emit('click', params)
}

const toggleLegendItem = (index) => {
  if (chartInstance.value) {
    chartInstance.value.dispatchAction({
      type: 'legendToggleSelect',
      name: legendData.value[index].name
    })
  }
  emit('legendClick', legendData.value[index])
}

// 暴露方法
defineExpose({
  chartInstance,
  refresh: () => chartInstance.value?.resize(),
  updateData: (newData) => {
    // 可以在这里实现数据更新逻辑
  }
})
</script>

<style scoped>
.customs-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-title {
  padding: 16px 0 8px 0;
  text-align: center;
}

.chart-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
}

.chart-subtitle {
  margin-top: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 12px 0;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.legend-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-text {
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.legend-value {
  color: #ffffff;
  font-weight: 500;
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-title h3 {
    font-size: 14px;
  }
  
  .chart-legend {
    gap: 8px;
  }
  
  .legend-item {
    font-size: 11px;
    padding: 3px 6px;
  }
}
</style>
