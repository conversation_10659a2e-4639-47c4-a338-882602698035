<template>
  <div class="equipment-stats">
    <!-- 数据卡片 -->
    <el-row :gutter="16" class="data-cards">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>生产设备总量</span>
              <el-tag size="small">月度</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="value">{{ loading ? '--' : '2,345' }}</div>
            <div class="trend">
              <span class="label">同比</span>
              <span class="percent up">+18.3%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>设备总值</span>
              <el-tag size="small" type="success">月度</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="value">{{ loading ? '--' : '¥5.67亿' }}</div>
            <div class="trend">
              <span class="label">同比</span>
              <span class="percent up">+22.5%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>负面清单数</span>
              <el-tag size="small" type="warning">月度</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="value">{{ loading ? '--' : '156' }}</div>
            <div class="trend">
              <span class="label">同比</span>
              <span class="percent down">-5.2%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="16">
        <!-- 设备类型分布 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">设备类型分布</div>
            <div class="chart-content">
              <v-chart :option="equipmentTypeOption" autoresize />
            </div>
          </div>
        </el-col>
        <!-- 设备价值趋势 -->
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">设备价值趋势</div>
            <div class="chart-content">
              <v-chart :option="valueTrendOption" autoresize />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import VChart from 'vue-echarts'

// 接收属性
const props = defineProps({
  loading: Boolean,
  data: Object
})

// 设备类型分布图表配置
const equipmentTypeOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    right: '5%',
    top: 'middle',
    textStyle: {
      color: 'var(--text-color)'
    }
  },
  series: [
    {
      type: 'pie',
      radius: ['50%', '70%'],
      center: ['40%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 420, name: '机械设备' },
        { value: 310, name: '电子设备' },
        { value: 280, name: '运输设备' },
        { value: 230, name: '检测设备' },
        { value: 160, name: '其他' }
      ]
    }
  ]
}))

// 设备价值趋势图表配置
const valueTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['设备总值', '新增设备值'],
    textStyle: {
      color: 'var(--text-color)'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisLine: {
      lineStyle: {
        color: 'var(--text-color-secondary)'
      }
    },
    axisLabel: {
      color: 'var(--text-color)'
    }
  },
  yAxis: {
    type: 'value',
    name: '金额（万元）',
    axisLine: {
      lineStyle: {
        color: 'var(--text-color-secondary)'
      }
    },
    axisLabel: {
      color: 'var(--text-color)'
    }
  },
  series: [
    {
      name: '设备总值',
      type: 'line',
      smooth: true,
      lineStyle: {
        width: 3,
        shadowColor: 'rgba(0,0,0,0.4)',
        shadowBlur: 10,
        shadowOffsetY: 10
      },
      data: [45000, 48000, 52000, 56000, 58000, 62000]
    },
    {
      name: '新增设备值',
      type: 'bar',
      barWidth: '30%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'var(--primary-color)'
          }, {
            offset: 1,
            color: 'var(--success-color)'
          }]
        }
      },
      data: [3000, 3500, 4200, 4800, 5100, 5600]
    }
  ]
}))
</script>

<style scoped>
.equipment-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.data-cards {
  margin-bottom: var(--content-gap);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  text-align: center;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8px;
}

.trend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.label {
  color: var(--text-color-secondary);
}

.percent {
  font-weight: 500;
}

.percent.up {
  color: var(--success-color);
}

.percent.down {
  color: var(--danger-color);
}

.charts-container {
  flex: 1;
  min-height: 0;
}

.chart-wrapper {
  height: 100%;
  padding: var(--content-padding);
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;
}

.chart-content {
  height: calc(100% - 32px);
}
</style>