// 交通运输工具模拟数据
export const transportStats = {
  zeroTaxCars: {
    value: 3456,
    unit: '辆',
    trend: '+18.5%'
  },
  postClosurePurchases: {
    value: 789,
    unit: '辆',
    trend: '+12.3%'
  },
  maintenanceCount: {
    value: 2345,
    unit: '次',
    trend: '+9.8%'
  },
  spareParts: {
    value: 567.89,
    unit: '万元',
    trend: '+15.6%'
  }
}

export const vehicleTypes = {
  labels: ['小型轿车', 'SUV', '商务车', '货车', '其他'],
  datasets: [{
    data: [40, 30, 15, 10, 5],
    backgroundColor: [
      '#ff6384',
      '#36a2eb',
      '#ffce56',
      '#4bc0c0',
      '#9966ff'
    ]
  }]
}

export const monthlyRegistrations = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '零关税车辆登记数',
    data: [80, 100, 120, 150, 180, 210],
    borderColor: '#36a2eb',
    tension: 0.4
  }]
}

export const maintenanceAnalysis = {
  labels: ['发动机', '变速箱', '底盘', '电气系统', '车身'],
  datasets: [{
    label: '维修次数',
    data: [300, 250, 200, 150, 100],
    backgroundColor: '#ff6384'
  }]
}

export const regionDistribution = {
  labels: ['海口', '三亚', '儋州', '琼海', '文昌'],
  datasets: [{
    label: '车辆数量',
    data: [800, 600, 400, 300, 200],
    backgroundColor: '#36a2eb'
  }]
}

export const sparePartsAnalysis = {
  labels: ['原厂配件', '副厂配件', '进口配件', '国产配件', '其他'],
  datasets: [{
    label: '使用比例',
    data: [35, 25, 20, 15, 5],
    backgroundColor: [
      '#ff6384',
      '#36a2eb',
      '#ffce56',
      '#4bc0c0',
      '#9966ff'
    ]
  }]
}

export const maintenanceTop5 = {
  labels: ['维修点A', '维修点B', '维修点C', '维修点D', '维修点E'],
  datasets: [{
    label: '维修量',
    data: [500, 400, 300, 200, 100],
    backgroundColor: '#4bc0c0'
  }]
}