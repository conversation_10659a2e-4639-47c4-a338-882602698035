import { ref, reactive } from 'vue'
import { mockDataService } from '../services/mockDataService'

export function useProcessingData() {
  const processingData = reactive({
    erpCompanies: {
      categories: ['一月', '二月', '三月', '四月', '五月', '六月'],
      series: [
        {
          name: '数量',
          data: [8, 10, 12, 15, 18, 22]
        }
      ]
    },
    topRegions: {
      categories: ['海口海关', '洋浦海关', '海口港区', '三亚海关', '马村海关'],
      series: [
        {
          name: '企业数量',
          data: [18, 15, 12, 8, 6]
        }
      ]
    },
    topCompanies: {
      categories: ['企业A', '企业B', '企业C', '企业D', '企业E'],
      series: [
        {
          name: '产值(万元)',
          data: [2500, 2200, 1800, 1500, 1200]
        }
      ]
    }
  })

  const loadProcessingData = async () => {
    try {
      const data = await mockDataService.getProcessingData()
      Object.assign(processingData, data)
    } catch (error) {
      console.error('加载加工增值数据失败:', error)
    }
  }

  return {
    processingData,
    loadProcessingData
  }
}
