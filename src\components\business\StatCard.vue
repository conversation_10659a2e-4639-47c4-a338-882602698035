<template>
  <base-card :loading="loading" :error="error" @retry="$emit('retry')">
    <div class="stat-card flex flex-col gap-2">
      <!-- 主要数值 -->
      <div class="stat-value flex items-center gap-1">
        <span class="text-2xl font-bold text-primary">{{ formattedValue }}</span>
        <span class="text-sm text-secondary" v-if="unit">{{ unit }}</span>
      </div>

      <!-- 标题和描述 -->
      <div class="stat-info">
        <h4 class="text-lg font-medium text-primary m-0">{{ title }}</h4>
        <p v-if="description" class="text-sm text-secondary mt-1 mb-0">{{ description }}</p>
      </div>

      <!-- 趋势指标 -->
      <div class="mt-1" v-if="trend">
        <trend-indicator
          :value="trend.value"
          :type="trend.type"
          :description="trend.description"
        />
      </div>
    </div>
  </base-card>
</template>

<script setup>
import { computed } from 'vue'
import BaseCard from '../base/Card.vue'
import TrendIndicator from '../indicators/TrendIndicator.vue'
import { useFormatter } from '@/composables/useFormatter'

const props = defineProps({
  value: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  unit: {
    type: String,
    default: ''
  },
  format: {
    type: String,
    default: 'number',
    validator: (value) => ['number', 'currency', 'percent'].includes(value)
  },
  trend: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['retry'])

// 格式化数值
const { formatNumber, formatCurrency, formatPercent } = useFormatter()

const formattedValue = computed(() => {
  switch (props.format) {
    case 'currency':
      return formatCurrency(props.value)
    case 'percent':
      return formatPercent(props.value)
    default:
      return formatNumber(props.value)
  }
})
</script>