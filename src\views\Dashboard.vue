<template>
  <div class="dashboard" :class="deviceClass">
    <!-- 头部标题 -->
    <header class="dashboard-header">
      <h1>海南海关领导视窗</h1>
      <div class="device-indicator">{{ deviceType }}</div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 加工增值模块 -->
      <section class="module processing-module">
        <h2>加工增值业务统计</h2>

        <!-- 核心统计数据 -->
        <div class="stats-overview">
          <div class="stat-card">
            <h3>货物总值</h3>
            <div class="stat-value">{{ processingData.cargoStats?.totalValue || 0 }}</div>
            <div class="stat-unit">万元</div>
          </div>
          <div class="stat-card">
            <h3>货物总量</h3>
            <div class="stat-value">{{ processingData.cargoStats?.totalVolume || 0 }}</div>
            <div class="stat-unit">吨</div>
          </div>
          <div class="stat-card">
            <h3>免征税款</h3>
            <div class="stat-value">{{ processingData.taxExemption?.totalAmount || 0 }}</div>
            <div class="stat-unit">万元</div>
          </div>
          <div class="stat-card">
            <h3>备案企业</h3>
            <div class="stat-value">{{ processingData.registeredCompanies?.total || 0 }}</div>
            <div class="stat-unit">家</div>
          </div>
        </div>

        <!-- 图表网格 -->
        <div class="charts-grid">
          <ChartCard
            title="货物总值及货量月度趋势"
            :data="processingData.cargoStats?.monthlyTrend"
            chart-type="line"
          />
          <ChartCard
            title="免征税款月度趋势"
            :data="processingData.taxExemption?.monthlyTrend"
            chart-type="bar"
          />
          <ChartCard
            title="免征税款地区分布"
            :data="processingData.taxExemption?.byRegion"
            chart-type="horizontal-bar"
          />
          <ChartCard
            title="备案企业类型分布"
            :data="processingData.registeredCompanies?.byType"
            chart-type="pie"
          />
          <ChartCard
            title="ERP联网企业趋势"
            :data="processingData.erpCompanies?.monthlyTrend"
            chart-type="line"
          />
          <ChartCard
            title="重点企业类型分布"
            :data="processingData.processingCompanies?.keyCompanyTypes"
            chart-type="pie"
          />
          <ChartCard
            title="主要地区企业分布"
            :data="processingData.processingCompanies?.regionDistribution"
            chart-type="bar"
          />
          <ChartCard
            title="加工增值产值TOP5企业"
            :data="processingData.processingCompanies?.top5Companies"
            chart-type="horizontal-bar"
          />
        </div>
      </section>

      <!-- 交通运输工具模块 -->
      <section class="module transport-module">
        <h2>交通运输工具业务统计</h2>

        <!-- 核心统计数据 -->
        <div class="stats-overview">
          <div class="stat-card">
            <h3>零关税车辆</h3>
            <div class="stat-value">{{ transportData.zeroDutyVehicles?.total || 0 }}</div>
            <div class="stat-unit">辆</div>
          </div>
          <div class="stat-card">
            <h3>免税车辆</h3>
            <div class="stat-value">{{ transportData.dutyFreeVehicles?.total || 0 }}</div>
            <div class="stat-unit">辆</div>
          </div>
          <div class="stat-card">
            <h3>维修车辆</h3>
            <div class="stat-value">{{ transportData.mainlandVehicleRepair?.total || 0 }}</div>
            <div class="stat-unit">辆</div>
          </div>
        </div>

        <!-- 图表网格 -->
        <div class="charts-grid">
          <ChartCard
            title="零关税进口车辆月度趋势"
            :data="transportData.zeroDutyVehicles?.monthlyTrend"
            chart-type="line"
          />
          <ChartCard
            title="零关税车辆类型分布"
            :data="transportData.zeroDutyVehicles?.byType"
            chart-type="pie"
          />
          <ChartCard
            title="免税车辆月度趋势"
            :data="transportData.dutyFreeVehicles?.monthlyTrend"
            chart-type="line"
          />
          <ChartCard
            title="免税车辆类别分布"
            :data="transportData.dutyFreeVehicles?.byCategory"
            chart-type="pie"
          />
          <ChartCard
            title="内地车辆维修趋势"
            :data="transportData.mainlandVehicleRepair?.monthlyTrend"
            chart-type="bar"
          />
          <ChartCard
            title="交通工具减免税费企业排行"
            :data="transportData.taxReductionRanking"
            chart-type="horizontal-bar"
          />
          <ChartCard
            title="生产设备减免税费企业排行"
            :data="transportData.equipmentTaxReduction"
            chart-type="horizontal-bar"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import ChartCard from '../components/ChartCard.vue'
import { useDeviceDetection } from '../composables/useDeviceDetection'
import { useProcessingData } from '../composables/useProcessingData'
import { useTransportData } from '../composables/useTransportData'

export default {
  name: 'Dashboard',
  components: {
    ChartCard
  },
  setup() {
    const { deviceType, deviceClass } = useDeviceDetection()
    const { processingData } = useProcessingData()
    const { transportData } = useTransportData()

    return {
      deviceType,
      deviceClass,
      processingData,
      transportData
    }
  }
}
</script>

<style scoped>
.dashboard {
  width: 100%;
  height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #2a3f5f;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.device-indicator {
  padding: 8px 16px;
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 20px;
  font-size: 0.9rem;
}

.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 40px;
  height: calc(100vh - 140px);
  overflow-y: auto;
}

.module {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.module h2 {
  font-size: 1.8rem;
  margin-bottom: 25px;
  color: #00d4ff;
  text-align: center;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

.stat-card h3 {
  font-size: 0.9rem;
  color: #ffffff;
  margin-bottom: 10px;
  opacity: 0.8;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 5px;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-unit {
  font-size: 0.8rem;
  color: #ffffff;
  opacity: 0.6;
}

.charts-grid {
  display: grid;
  gap: 20px;
}

/* 移动端样式 */
.dashboard.mobile .charts-grid {
  grid-template-columns: 1fr;
}

.dashboard.mobile .stats-overview {
  grid-template-columns: repeat(2, 1fr);
}

.dashboard.mobile .dashboard-header h1 {
  font-size: 1.8rem;
}

.dashboard.mobile .stat-value {
  font-size: 1.8rem;
}

/* PC端样式 */
.dashboard.pc .charts-grid {
  grid-template-columns: repeat(3, 1fr);
}

.dashboard.pc .stats-overview {
  grid-template-columns: repeat(4, 1fr);
}

/* 大屏墙面样式 */
.dashboard.wall .charts-grid {
  grid-template-columns: repeat(4, 1fr);
}

.dashboard.wall .stats-overview {
  grid-template-columns: repeat(6, 1fr);
}

.dashboard.wall .dashboard-header h1 {
  font-size: 3.5rem;
}

.dashboard.wall .module h2 {
  font-size: 2.2rem;
}

.dashboard.wall .stat-value {
  font-size: 2.8rem;
}

.dashboard.wall .stat-card {
  padding: 25px;
}
</style>
