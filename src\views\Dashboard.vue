<template>
  <div class="dashboard" :class="deviceClass">
    <!-- 头部标题 -->
    <header class="dashboard-header">
      <h1>海南海关领导视窗</h1>
      <div class="device-indicator">{{ deviceType }}</div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 加工增值模块 -->
      <section class="module processing-module">
        <h2>加工增值</h2>
        <div class="charts-grid">
          <ChartCard 
            title="ERP联网企业数量"
            :data="processingData.erpCompanies"
            chart-type="bar"
          />
          <ChartCard 
            title="TOP5关区企业分布"
            :data="processingData.topRegions"
            chart-type="horizontal-bar"
          />
          <ChartCard 
            title="TOP5企业产值排行"
            :data="processingData.topCompanies"
            chart-type="bar"
          />
        </div>
      </section>

      <!-- 交通运输工具模块 -->
      <section class="module transport-module">
        <h2>交通运输工具</h2>
        <div class="charts-grid">
          <ChartCard 
            title="零关税进口车辆统计"
            :data="transportData.vehicles"
            chart-type="line"
          />
          <ChartCard 
            title="车辆类型分布"
            :data="transportData.vehicleTypes"
            chart-type="pie"
          />
          <ChartCard 
            title="减免税费企业排行"
            :data="transportData.taxReduction"
            chart-type="bar"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import ChartCard from '../components/ChartCard.vue'
import { useDeviceDetection } from '../composables/useDeviceDetection'
import { useProcessingData } from '../composables/useProcessingData'
import { useTransportData } from '../composables/useTransportData'

export default {
  name: 'Dashboard',
  components: {
    ChartCard
  },
  setup() {
    const { deviceType, deviceClass } = useDeviceDetection()
    const { processingData } = useProcessingData()
    const { transportData } = useTransportData()

    return {
      deviceType,
      deviceClass,
      processingData,
      transportData
    }
  }
}
</script>

<style scoped>
.dashboard {
  width: 100%;
  height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #2a3f5f;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.device-indicator {
  padding: 8px 16px;
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 20px;
  font-size: 0.9rem;
}

.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 40px;
  height: calc(100vh - 140px);
  overflow-y: auto;
}

.module {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.module h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #00d4ff;
  text-align: center;
}

.charts-grid {
  display: grid;
  gap: 20px;
}

/* 移动端样式 */
.dashboard.mobile .charts-grid {
  grid-template-columns: 1fr;
}

.dashboard.mobile .dashboard-header h1 {
  font-size: 1.8rem;
}

/* PC端样式 */
.dashboard.pc .charts-grid {
  grid-template-columns: repeat(3, 1fr);
}

/* 大屏墙面样式 */
.dashboard.wall .charts-grid {
  grid-template-columns: repeat(4, 1fr);
}

.dashboard.wall .dashboard-header h1 {
  font-size: 3.5rem;
}

.dashboard.wall .module h2 {
  font-size: 2.2rem;
}
</style>
