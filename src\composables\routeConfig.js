/**
 * 设备类型路由映射配置
 */
export const deviceRouteMap = {
  processing: {
    pc: () => import('@/views/processing/pc/index.vue'),
    mobile: () => import('@/views/processing/mobile/index.vue'),
    wall: () => import('@/views/processing/wall/index.vue')
  },
  transport: {
    pc: () => import('@/views/transport/pc/index.vue'),
    mobile: () => import('@/views/transport/mobile/index.vue'),
    wall: () => import('@/views/transport/wall/index.vue')
  }
}

/**
 * 路由配置
 */
export const routeConfig = {
  // 默认路由
  defaultRoute: '/processing',
  // 登录路由
  loginRoute: '/login',
  // 错误路由
  errorRoute: '/error',
  // 支持的设备类型
  supportedDevices: ['pc', 'mobile', 'wall'],
  // 错误类型
  errorTypes: {
    invalidDevice: 'invalid-device',
    notFound: 'not-found',
    serverError: 'server-error'
  },
  // 页面标题
  title: {
    suffix: '港口领导驾驶舱',
    separator: ' - '
  }
}