<template>
  <ChartCard title="零关税车辆统计" :options="chartOptions" />
</template>

<script setup>
import { computed } from 'vue';
import ChartCard from '../../../components/ChartCard.vue';
import { useThemeStore } from '../../../stores/theme.js';

// 接收父组件传递的数据
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 获取主题状态
const themeStore = useThemeStore();

// 计算图表配置
const chartOptions = computed(() => {
  // 确保数据存在
  if (!props.data || !props.data.monthlyTrend || props.data.monthlyTrend.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    };
  }

  // 提取月份和数量数据
  const months = props.data.monthlyTrend.map(item => `${item.month}月`);
  const counts = props.data.monthlyTrend.map(item => item.count);
  
  // 计算累计数量
  let accumulatedCounts = [];
  let sum = 0;
  for (const count of counts) {
    sum += count;
    accumulatedCounts.push(sum);
  }
  
  // 图表配置
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['月度数量', '累计数量'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      }
    ],
    series: [
      {
        name: '月度数量',
        type: 'bar',
        barWidth: '40%',
        data: counts,
        itemStyle: {
          color: themeStore.isDark ? '#f89a80' : '#F56C6C'
        }
      },
      {
        name: '累计数量',
        type: 'line',
        data: accumulatedCounts,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: themeStore.isDark ? '#5d80f4' : '#409EFF'
        },
        itemStyle: {
          color: themeStore.isDark ? '#5d80f4' : '#409EFF'
        }
      }
    ]
  };
});
</script>