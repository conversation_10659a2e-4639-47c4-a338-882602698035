<template>
  <BaseChart
    :option="chartOption"
    :loading="loading"
    :error="error"
    :isEmpty="isEmpty"
    :width="width"
    :height="height"
    @ready="handleReady"
    @click="handleClick"
  />
</template>

<script setup>
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'

// Props定义
const props = defineProps({
  // 数据
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // X轴数据
  xAxisData: {
    type: Array,
    default: () => []
  },
  // 系列名称
  seriesName: {
    type: String,
    default: '数据'
  },
  // 颜色
  color: {
    type: [String, Array],
    default: '#1890ff'
  },
  // 是否显示数值标签
  showLabel: {
    type: Boolean,
    default: true
  },
  // 是否显示网格线
  showGrid: {
    type: Boolean,
    default: true
  },
  // 柱子宽度
  barWidth: {
    type: [String, Number],
    default: 'auto'
  },
  // 图表方向
  direction: {
    type: String,
    default: 'vertical', // vertical | horizontal
    validator: value => ['vertical', 'horizontal'].includes(value)
  },
  // 容器尺寸
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '400px'
  },
  // 状态
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['ready', 'click'])

// 计算属性
const isEmpty = computed(() => {
  return !props.data || props.data.length === 0
})

const chartOption = computed(() => {
  if (isEmpty.value) return {}

  const isHorizontal = props.direction === 'horizontal'
  
  // 处理数据
  const processedData = props.data.map(item => {
    if (typeof item === 'object') {
      return {
        name: item.name || item.label,
        value: item.value || item.count || 0
      }
    }
    return { name: String(item), value: item }
  })

  const names = processedData.map(item => item.name)
  const values = processedData.map(item => item.value)

  return {
    title: props.title ? {
      text: props.title,
      left: 'center',
      top: 20,
      textStyle: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1890ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>${param.seriesName}: ${param.value}`
      }
    },
    
    grid: {
      left: isHorizontal ? '15%' : '10%',
      right: '10%',
      top: props.title ? '15%' : '10%',
      bottom: '15%',
      containLabel: true
    },
    
    xAxis: {
      type: isHorizontal ? 'value' : 'category',
      data: isHorizontal ? undefined : (props.xAxisData.length ? props.xAxisData : names),
      axisLine: {
        lineStyle: {
          color: '#4a5568'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#4a5568'
        }
      },
      axisLabel: {
        color: '#a0aec0',
        fontSize: 12
      },
      splitLine: props.showGrid ? {
        lineStyle: {
          color: '#2d3748',
          type: 'dashed'
        }
      } : { show: false }
    },
    
    yAxis: {
      type: isHorizontal ? 'category' : 'value',
      data: isHorizontal ? (props.xAxisData.length ? props.xAxisData : names) : undefined,
      axisLine: {
        lineStyle: {
          color: '#4a5568'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#4a5568'
        }
      },
      axisLabel: {
        color: '#a0aec0',
        fontSize: 12
      },
      splitLine: props.showGrid ? {
        lineStyle: {
          color: '#2d3748',
          type: 'dashed'
        }
      } : { show: false }
    },
    
    series: [{
      name: props.seriesName,
      type: 'bar',
      data: values,
      barWidth: props.barWidth,
      itemStyle: {
        color: Array.isArray(props.color) 
          ? function(params) {
              return props.color[params.dataIndex % props.color.length]
            }
          : props.color,
        borderRadius: isHorizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
      },
      label: props.showLabel ? {
        show: true,
        position: isHorizontal ? 'right' : 'top',
        color: '#fff',
        fontSize: 12,
        formatter: '{c}'
      } : { show: false },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(24, 144, 255, 0.5)'
        }
      }
    }],
    
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }
})

// 事件处理
const handleReady = (chartInstance) => {
  emit('ready', chartInstance)
}

const handleClick = (params) => {
  emit('click', params)
}
</script>
