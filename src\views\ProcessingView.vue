<template>
  <div class="processing-view">
    <ProcessingChart :data="chartData" />
  </div>
</template>
<script>
import { ref, onMounted } from 'vue'
import { useProcessingData } from '../middleware/processing'
import ProcessingChart from '../components/ProcessingChart.vue'

export default {
  name: 'ProcessingView',
  components: { ProcessingChart },
  setup() {
    const chartData = ref({})
    onMounted(async () => {
      chartData.value = await useProcessingData()
    })
    return { chartData }
  }
}
</script>
<style scoped>
.processing-view { width: 100vw; height: 100vh; }
</style> 