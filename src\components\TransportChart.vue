<template>
  <div>
    <div class="cards">
      <div v-for="(v, k) in data.stats" :key="k" class="card">{{ k }}: {{ v }}</div>
    </div>
    <div class="trend">
      <div>月度趋势（区内）: {{ data.carImportTrend?.区内?.map(i=>i.value).join(', ') }}</div>
      <div>月度趋势（区外）: {{ data.carImportTrend?.区外?.map(i=>i.value).join(', ') }}</div>
    </div>
    <div class="pie">运输类型分布: {{ data.transportType?.map(i=>i.name+':'+i.value).join(', ') }}</div>
    <div class="top5">TOP5关区: {{ data.carTop5Customs?.map(i=>i.name+':'+i.value).join(', ') }}</div>
    <div class="company-table">企业表格: {{ data.companyTable?.map(i=>i.company+':'+i.value).join(', ') }}</div>
    <div class="color-blocks">色块分布: {{ data.colorBlocks?.map(i=>i.name+':'+i.value).join(', ') }}</div>
  </div>
</template>
<script>
export default {
  name: 'TransportChart',
  props: { data: Object }
}
</script>
<style scoped>
.card { display:inline-block; margin:4px; padding:4px; border:1px solid #eee; }
</style> 