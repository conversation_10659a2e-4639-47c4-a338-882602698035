/**
 * 海南海关领导视窗 - 统一数据服务
 * 整合API调用、数据处理和模拟数据
 */

import { customsAPI } from '../utils/request.js'
import { 
  processingBusinessData, 
  transportBusinessData, 
  analyticsData,
  getAllMockData 
} from './mockDataService.js'

// 数据服务配置
const DATA_CONFIG = {
  USE_MOCK: process.env.NODE_ENV === 'development', // 开发环境使用模拟数据
  CACHE_TTL: 5 * 60 * 1000, // 缓存5分钟
  RETRY_TIMES: 3
}

// 数据缓存
const dataCache = new Map()

/**
 * 缓存管理
 */
const cacheManager = {
  get(key) {
    const cached = dataCache.get(key)
    if (cached && Date.now() - cached.timestamp < DATA_CONFIG.CACHE_TTL) {
      return cached.data
    }
    dataCache.delete(key)
    return null
  },

  set(key, data) {
    dataCache.set(key, {
      data,
      timestamp: Date.now()
    })
  },

  clear(pattern) {
    if (pattern) {
      for (const key of dataCache.keys()) {
        if (key.includes(pattern)) {
          dataCache.delete(key)
        }
      }
    } else {
      dataCache.clear()
    }
  }
}

/**
 * 数据获取器 - 支持真实API和模拟数据切换
 */
const dataFetcher = {
  async fetchWithFallback(apiCall, mockCall, cacheKey) {
    // 检查缓存
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }

    let result
    
    if (DATA_CONFIG.USE_MOCK) {
      // 使用模拟数据
      result = await mockCall()
    } else {
      try {
        // 尝试调用真实API
        const response = await apiCall()
        result = response.success ? response.data : await mockCall()
      } catch (error) {
        console.warn(`API调用失败，使用模拟数据: ${error.message}`)
        result = await mockCall()
      }
    }

    // 缓存结果
    cacheManager.set(cacheKey, result)
    return result
  }
}

/**
 * 加工增值业务数据服务
 */
export const processingDataService = {
  // 获取基础统计数据
  async getBasicStats() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'basic' }),
      () => Promise.resolve(processingBusinessData.getBasicStats()),
      'processing_basic_stats'
    )
  },

  // 获取货物统计
  async getGoodsStats() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'goods' }),
      () => Promise.resolve(processingBusinessData.getGoodsStats()),
      'processing_goods_stats'
    )
  },

  // 获取免征税额数据
  async getTaxExemptionData() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'tax' }),
      () => Promise.resolve(processingBusinessData.getTaxExemptionData()),
      'processing_tax_data'
    )
  },

  // 获取企业统计
  async getEnterpriseStats() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'enterprise' }),
      () => Promise.resolve(processingBusinessData.getRegisteredEnterprises()),
      'processing_enterprise_stats'
    )
  },

  // 获取月度趋势
  async getValueTrend() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'trend' }),
      () => Promise.resolve(processingBusinessData.getValueTrend()),
      'processing_value_trend'
    )
  },

  // 获取企业类型分布
  async getCompanyTypes() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'company_types' }),
      () => Promise.resolve(processingBusinessData.getCompanyTypeDistribution()),
      'processing_company_types'
    )
  },

  // 获取TOP5企业
  async getTop5Companies() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'top_companies' }),
      () => Promise.resolve(processingBusinessData.getTop5Companies()),
      'processing_top5_companies'
    )
  },

  // 获取ERP联网企业数据
  async getERPData() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'erp' }),
      () => Promise.resolve(processingBusinessData.getERPEnterprises()),
      'processing_erp_data'
    )
  },

  // 获取地区分布
  async getRegionDistribution() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('processing', { type: 'regions' }),
      () => Promise.resolve(processingBusinessData.getCityDistribution()),
      'processing_region_distribution'
    )
  },

  // 获取完整的加工增值数据（兼容原有接口）
  async getProcessingData() {
    const [
      basicStats,
      valueTrend,
      companyTypes,
      top5Companies,
      erpData,
      cityDistribution,
      areaDistribution
    ] = await Promise.all([
      this.getBasicStats(),
      this.getValueTrend(),
      this.getCompanyTypes(),
      this.getTop5Companies(),
      this.getERPData(),
      processingBusinessData.getCityDistribution(),
      processingBusinessData.getAreaDistribution()
    ])

    return {
      stats: basicStats,
      valueTrend,
      companyType: companyTypes,
      top5Company: top5Companies,
      erpCompanyCount: erpData.total,
      erpTop5Customs: erpData.topCustoms,
      cityTable: cityDistribution,
      areaTable: areaDistribution,
      colorBlocks: processingBusinessData.getColorBlocks?.() || [],
      tree: {
        name: '海南',
        children: [
          { name: '海口', value: 100 },
          { name: '三亚', value: 80 }
        ]
      }
    }
  }
}

/**
 * 交通运输业务数据服务
 */
export const transportDataService = {
  // 获取零关税车辆数据
  async getZeroTariffCars() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('transport', { type: 'zero_tariff_cars' }),
      () => Promise.resolve(transportBusinessData.getZeroTariffCars()),
      'transport_zero_tariff_cars'
    )
  },

  // 获取免税交通工具数据
  async getDutyFreeVehicles() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('transport', { type: 'duty_free_vehicles' }),
      () => Promise.resolve(transportBusinessData.getDutyFreeVehicles()),
      'transport_duty_free_vehicles'
    )
  },

  // 获取零配件维修数据
  async getSparePartsData() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('transport', { type: 'spare_parts' }),
      () => Promise.resolve(transportBusinessData.getSparePartsRepair()),
      'transport_spare_parts'
    )
  },

  // 获取企业排行榜
  async getEnterpriseRanking() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('transport', { type: 'enterprise_ranking' }),
      () => Promise.resolve(transportBusinessData.getTransportEnterpriseRanking()),
      'transport_enterprise_ranking'
    )
  }
}

/**
 * 综合分析数据服务
 */
export const analyticsDataService = {
  // 获取关键指标
  async getKeyIndicators() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('analytics', { type: 'indicators' }),
      () => Promise.resolve(analyticsData.getKeyIndicators()),
      'analytics_key_indicators'
    )
  },

  // 获取地区分布
  async getRegionDistribution() {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('analytics', { type: 'regions' }),
      () => Promise.resolve(analyticsData.getRegionDistribution()),
      'analytics_region_distribution'
    )
  },

  // 获取时间趋势
  async getTimeTrend(type = 'monthly', months = 12) {
    return dataFetcher.fetchWithFallback(
      () => customsAPI.getStatistics('analytics', { type: 'trend', period: type, months }),
      () => Promise.resolve(analyticsData.getTimeTrend(type, months)),
      `analytics_time_trend_${type}_${months}`
    )
  }
}

/**
 * 实时数据服务
 */
export const realTimeDataService = {
  // 获取实时数据
  async getRealTimeData() {
    try {
      const response = await customsAPI.getRealTimeData()
      return response.success ? response.data : null
    } catch (error) {
      console.warn('实时数据获取失败:', error.message)
      return null
    }
  },

  // 启动实时数据监听
  startRealTimeMonitoring(callback, interval = 30000) {
    const monitor = async () => {
      const data = await this.getRealTimeData()
      if (data && callback) {
        callback(data)
      }
    }

    monitor() // 立即执行一次
    return setInterval(monitor, interval)
  },

  // 停止实时数据监听
  stopRealTimeMonitoring(intervalId) {
    if (intervalId) {
      clearInterval(intervalId)
    }
  }
}

/**
 * 数据服务管理器
 */
export const dataServiceManager = {
  // 清除所有缓存
  clearAllCache() {
    cacheManager.clear()
  },

  // 清除特定模块缓存
  clearModuleCache(module) {
    cacheManager.clear(module)
  },

  // 切换数据源
  setUseMock(useMock) {
    DATA_CONFIG.USE_MOCK = useMock
    this.clearAllCache()
  },

  // 获取缓存状态
  getCacheStatus() {
    return {
      size: dataCache.size,
      keys: Array.from(dataCache.keys())
    }
  }
}

// 兼容原有接口
export const getProcessingData = processingDataService.getProcessingData
export const getTransportData = () => transportDataService.getZeroTariffCars()

// 导出所有模拟数据（用于测试）
export { getAllMockData }
