<template>
  <ChartCard title="免征税款趋势" :options="chartOptions" />
</template>

<script setup>
import { computed } from 'vue';
import ChartCard from '../../../components/ChartCard.vue';
import { useThemeStore } from '../../../stores/theme.js';
import { useDeviceStore } from '../../../stores/device.js';

// 接收父组件传递的数据
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 获取主题和设备状态
const themeStore = useThemeStore();
const deviceStore = useDeviceStore();

// 计算图表配置
const chartOptions = computed(() => {
  // 确保数据存在
  if (!props.data || !props.data.monthlyTrend || props.data.monthlyTrend.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    };
  }

  // 提取月份和金额数据
  const months = props.data.monthlyTrend.map(item => `${item.month}月`);
  const values = props.data.monthlyTrend.map(item => item.value);
  const counts = props.data.monthlyTrend.map(item => item.count);
  
  // 计算同比增长率
  const growthRate = [];
  for (let i = 1; i < values.length; i++) {
    const rate = values[i] > 0 && values[i-1] > 0 
      ? ((values[i] - values[i-1]) / values[i-1] * 100).toFixed(1) 
      : 0;
    growthRate.push(parseFloat(rate));
  }
  growthRate.unshift(0); // 第一个月没有同比数据
  
  // 图表配置
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['免征税款', '业务量', '同比增长'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额/数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '增长率(%)',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '免征税款',
        type: 'bar',
        barWidth: deviceStore.isMobile ? '25%' : '20%',
        data: values,
        itemStyle: {
          color: themeStore.isDark ? '#f89a80' : '#F56C6C'
        }
      },
      {
        name: '业务量',
        type: 'bar',
        barWidth: deviceStore.isMobile ? '25%' : '20%',
        data: counts,
        itemStyle: {
          color: themeStore.isDark ? '#5d80f4' : '#409EFF'
        }
      },
      {
        name: '同比增长',
        type: 'line',
        yAxisIndex: 1,
        data: growthRate,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: themeStore.isDark ? '#63e2b7' : '#67C23A'
        },
        itemStyle: {
          color: themeStore.isDark ? '#63e2b7' : '#67C23A'
        }
      }
    ]
  };
});
</script>