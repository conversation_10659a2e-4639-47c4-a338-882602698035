/**
 * 海关图表组件库
 * 统一导出所有图表组件
 */

import BaseChart from './BaseChart.vue'
import CustomsChart from './CustomsChart.vue'

// 预设的海关业务图表配置
export const CUSTOMS_CHART_PRESETS = {
  // 企业数量柱状图
  enterpriseBar: {
    type: 'bar',
    dataFormat: 'enterprise',
    config: {
      direction: 'vertical',
      showLabel: true,
      barWidth: '60%'
    }
  },

  // 货值趋势折线图
  valueTrend: {
    type: 'line',
    dataFormat: 'currency',
    config: {
      smooth: true,
      showArea: true,
      showLabel: false
    }
  },

  // 地区分布饼图
  regionPie: {
    type: 'pie',
    dataFormat: 'count',
    config: {
      radius: '70%',
      showLegend: true
    }
  },

  // 企业类型环形图
  enterpriseDoughnut: {
    type: 'doughnut',
    dataFormat: 'enterprise',
    config: {
      radius: ['40%', '70%'],
      showLegend: true
    }
  },

  // 车辆数量水平柱状图
  vehicleHorizontalBar: {
    type: 'bar',
    dataFormat: 'vehicle',
    config: {
      direction: 'horizontal',
      showLabel: true,
      barWidth: '50%'
    }
  },

  // 税额仪表盘
  taxGauge: {
    type: 'gauge',
    dataFormat: 'currency',
    config: {
      min: 0,
      max: 100,
      name: '完成率'
    }
  }
}

// 快速创建海关图表的工厂函数
export const createCustomsChart = (preset, data, options = {}) => {
  const config = CUSTOMS_CHART_PRESETS[preset]
  if (!config) {
    console.warn(`未找到预设配置: ${preset}`)
    return null
  }

  return {
    ...config,
    data,
    ...options
  }
}

// 海关业务图表组件
export const CustomsCharts = {
  // 基础图表
  BaseChart,
  CustomsChart,

  // 业务图表组件
  EnterpriseBarChart: (props) => ({
    ...CustomsChart,
    props: {
      ...props,
      ...CUSTOMS_CHART_PRESETS.enterpriseBar
    }
  }),

  ValueTrendChart: (props) => ({
    ...CustomsChart,
    props: {
      ...props,
      ...CUSTOMS_CHART_PRESETS.valueTrend
    }
  }),

  RegionPieChart: (props) => ({
    ...CustomsChart,
    props: {
      ...props,
      ...CUSTOMS_CHART_PRESETS.regionPie
    }
  }),

  EnterpriseDoughnutChart: (props) => ({
    ...CustomsChart,
    props: {
      ...props,
      ...CUSTOMS_CHART_PRESETS.enterpriseDoughnut
    }
  }),

  VehicleHorizontalBarChart: (props) => ({
    ...CustomsChart,
    props: {
      ...props,
      ...CUSTOMS_CHART_PRESETS.vehicleHorizontalBar
    }
  }),

  TaxGaugeChart: (props) => ({
    ...CustomsChart,
    props: {
      ...props,
      ...CUSTOMS_CHART_PRESETS.taxGauge
    }
  })
}

// 图表数据转换工具
export const chartDataTransformers = {
  // 转换企业排行数据
  transformEnterpriseRanking: (data) => {
    return data.map(item => ({
      name: item.name || item.enterprise,
      value: item.value || item.amount || 0,
      rank: item.rank || 0
    }))
  },

  // 转换地区分布数据
  transformRegionData: (data) => {
    return data.map(item => ({
      name: item.name || item.region || item.city,
      value: item.value || item.count || 0
    }))
  },

  // 转换时间序列数据
  transformTimeSeriesData: (data) => {
    return data.map(item => ({
      name: item.name || item.month || item.date,
      value: item.value || item.amount || 0
    }))
  },

  // 转换企业类型数据
  transformEnterpriseTypeData: (data) => {
    return data.map(item => ({
      name: item.name || item.type,
      value: item.value || item.count || 0
    }))
  }
}

// 图表主题配置
export const CUSTOMS_CHART_THEMES = {
  // 深蓝主题（默认）
  darkBlue: {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#ffffff'
    },
    colors: ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#f5222d', '#722ed1']
  },

  // 海关蓝主题
  customsBlue: {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#ffffff'
    },
    colors: ['#2f54eb', '#13c2c2', '#52c41a', '#faad14', '#fa541c', '#eb2f96']
  },

  // 渐变主题
  gradient: {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#ffffff'
    },
    colors: [
      {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#1890ff' },
          { offset: 1, color: '#36cfc9' }
        ]
      },
      {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#13c2c2' },
          { offset: 1, color: '#52c41a' }
        ]
      }
    ]
  }
}

// 导出所有组件和工具
export {
  BaseChart,
  CustomsChart
}

export default {
  BaseChart,
  CustomsChart,
  CUSTOMS_CHART_PRESETS,
  createCustomsChart,
  CustomsCharts,
  chartDataTransformers,
  CUSTOMS_CHART_THEMES
}
