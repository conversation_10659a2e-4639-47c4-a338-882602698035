<template>
  <div class="flex flex-col h-full bg-black text-white overflow-hidden relative">
    <!-- Canvas 背景 -->
    <canvas ref="bgCanvas" class="absolute top-0 left-0 w-full h-full opacity-20"></canvas>

    <!-- 顶部标题栏 -->
    <header class="flex items-center justify-between px-8 h-16 bg-gray-900/80 border-b border-gray-800 relative z-10">
      <div class="flex items-center gap-4">
        <h1 class="text-2xl font-bold text-blue-400">
          {{ currentModule }}大屏
        </h1>
        <span class="text-sm text-gray-400">
          {{ currentTime }}
        </span>
      </div>

      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded-md transition-colors"
          @click="refreshData"
        >
          <svg
            class="w-4 h-4 animate-spin"
            :class="{ 'opacity-0': !isRefreshing }"
            viewBox="0 0 24 24"
          >
            <path
              fill="currentColor"
              d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8zm0 16v2a10 10 0 0 0 10-10h-2a8 8 0 0 1-8 8z"
            />
          </svg>
          {{ isRefreshing ? '刷新中...' : '刷新数据' }}
        </button>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="flex-1 p-8 overflow-auto relative z-10">
      <slot></slot>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

// 获取当前路由
const route = useRoute()

// 当前模块名称
const currentModule = computed(() => {
  const path = route.path.split('/')[1]
  return path === 'processing' ? '加工增值' : '交通运输'
})

// 当前时间
const currentTime = ref('')
const isRefreshing = ref(false)

// 更新时间
const updateTime = () => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }
  currentTime.value = now.toLocaleString('zh-CN', options)
}

// 刷新数据
const refreshData = async () => {
  if (isRefreshing.value) return
  
  isRefreshing.value = true
  try {
    await emit('refresh')
  } finally {
    isRefreshing.value = false
  }
}

// Canvas 背景
const bgCanvas = ref(null)
let animationFrame = null

// 绘制背景
const drawBackground = () => {
  const canvas = bgCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const width = canvas.width = canvas.offsetWidth
  const height = canvas.height = canvas.offsetHeight

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 绘制网格
  ctx.strokeStyle = '#1a365d'
  ctx.lineWidth = 1

  const gridSize = 50
  const time = Date.now() / 3000

  for (let x = 0; x <= width; x += gridSize) {
    for (let y = 0; y <= height; y += gridSize) {
      const offsetX = Math.sin(y / 30 + time) * 5
      const offsetY = Math.cos(x / 30 + time) * 5

      ctx.beginPath()
      ctx.arc(x + offsetX, y + offsetY, 1, 0, Math.PI * 2)
      ctx.stroke()
    }
  }

  animationFrame = requestAnimationFrame(drawBackground)
}

// 定时器
let timer = null

// 组件挂载时
onMounted(() => {
  // 初始化时间
  updateTime()
  // 每秒更新时间
  timer = setInterval(updateTime, 1000)
  // 开始动画
  drawBackground()
})

// 组件卸载时
onUnmounted(() => {
  // 清除定时器
  if (timer) {
    clearInterval(timer)
  }
  // 停止动画
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})

// 定义事件
const emit = defineEmits(['refresh'])
</script>