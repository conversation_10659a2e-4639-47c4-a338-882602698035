/**
 * 仿真数据服务
 * 基于需求表生成模拟业务数据
 */

// 地区数据
const REGIONS = [
  '海口市', '三亚市', '儋州市', '琼海市', '万宁市', 
  '东方市', '五指山市', '文昌市', '陵水县', '乐东县'
]

// 企业类型
const ENTERPRISE_TYPES = [
  '加工增值企业', '贸易企业', '物流企业', '制造企业', '服务企业'
]

// 产品类别
const PRODUCT_CATEGORIES = [
  '电子产品', '纺织品', '食品加工', '化工产品', '机械设备',
  '医疗器械', '汽车配件', '建材产品', '农产品', '海产品'
]

// 交通工具类型
const VEHICLE_TYPES = [
  '小汽车', '货车', '客车', '摩托车', '电动车', '游艇', '船舶'
]

/**
 * 生成随机数
 */
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

function randomFloat(min, max, decimals = 2) {
  return parseFloat((Math.random() * (max - min) + min).toFixed(decimals))
}

/**
 * 生成随机日期
 */
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

/**
 * 生成月度时间序列
 */
function generateMonthlyData(months = 12, baseValue = 100, variance = 0.3) {
  const data = []
  const now = new Date()
  
  for (let i = months - 1; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const month = date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit' })
    const value = Math.round(baseValue * (1 + (Math.random() - 0.5) * variance))
    
    data.push({
      name: month,
      value: Math.max(0, value)
    })
  }
  
  return data
}

/**
 * 加工增值业务数据
 */
export const processingBusinessData = {
  // 货物总值及货量统计
  getGoodsStats() {
    return {
      totalValue: randomFloat(50000, 100000, 0),
      totalQuantity: randomInt(10000, 50000),
      categories: PRODUCT_CATEGORIES.slice(0, 6).map(category => ({
        name: category,
        value: randomFloat(5000, 15000, 0)
      })),
      monthlyTrend: generateMonthlyData(12, 8000, 0.4)
    }
  },

  // 免征税款统计
  getTaxExemptionData() {
    return {
      totalAmount: randomFloat(10000, 30000, 0),
      enterpriseCount: randomInt(200, 500),
      monthlyTrend: generateMonthlyData(12, 2500, 0.3),
      byCategory: PRODUCT_CATEGORIES.slice(0, 5).map(category => ({
        name: category,
        value: randomFloat(1000, 5000, 0)
      }))
    }
  },

  // 备案企业统计
  getRegisteredEnterprises() {
    return {
      total: randomInt(800, 1200),
      active: randomInt(600, 900),
      byType: ENTERPRISE_TYPES.map(type => ({
        name: type,
        value: randomInt(50, 200)
      })),
      byRegion: REGIONS.slice(0, 8).map(region => ({
        name: region,
        value: randomInt(20, 150)
      }))
    }
  },

  // 企业排行榜
  getEnterpriseRanking() {
    const enterprises = [
      '海南自贸港建设投资有限公司', '海南航空控股股份有限公司',
      '海南椰岛集团股份有限公司', '海南瑞泽新型建材股份有限公司',
      '海南海药股份有限公司', '海南双成药业股份有限公司',
      '海南神农科技股份有限公司', '海南康芝药业股份有限公司'
    ]

    return {
      byValue: enterprises.slice(0, 5).map((name, index) => ({
        rank: index + 1,
        name,
        value: randomFloat(5000, 20000, 0),
        change: randomFloat(-20, 30, 1)
      })),
      byTaxExemption: enterprises.slice(0, 5).map((name, index) => ({
        rank: index + 1,
        name,
        value: randomFloat(1000, 5000, 0),
        change: randomFloat(-15, 25, 1)
      }))
    }
  },

  // ERP联网企业数量
  getERPEnterprises() {
    return {
      total: randomInt(300, 600),
      inZone: randomInt(200, 400),
      outZone: randomInt(100, 200),
      monthlyGrowth: generateMonthlyData(6, 50, 0.2),
      topRegions: REGIONS.slice(0, 5).map(region => ({
        name: region,
        value: randomInt(20, 80)
      }))
    }
  },

  // 主要加工产品TOP5
  getTopProducts() {
    return PRODUCT_CATEGORIES.slice(0, 5).map((product, index) => ({
      rank: index + 1,
      name: product,
      value: randomFloat(3000, 12000, 0),
      percentage: randomFloat(10, 25, 1),
      trend: generateMonthlyData(6, 1000, 0.3)
    }))
  }
}

/**
 * 交通运输工具业务数据
 */
export const transportBusinessData = {
  // 零关税进口小汽车
  getZeroTariffCars() {
    const brands = ['奔驰', '宝马', '奥迪', '特斯拉', '保时捷', '雷克萨斯']
    
    return {
      totalCount: randomInt(500, 1200),
      totalValue: randomFloat(50000, 120000, 0),
      brands: brands.map(brand => ({
        name: brand,
        value: randomInt(50, 200)
      })),
      monthlyTrend: generateMonthlyData(12, 100, 0.4)
    }
  },

  // 免税交通运输工具
  getDutyFreeVehicles() {
    return {
      totalCount: randomInt(200, 500),
      categories: VEHICLE_TYPES.map(type => ({
        name: type,
        value: randomInt(20, 100)
      })),
      monthlyStats: generateMonthlyData(12, 40, 0.3)
    }
  },

  // 零配件维修统计
  getSparePartsRepair() {
    const partTypes = ['发动机配件', '制动系统', '电子设备', '车身配件', '轮胎轮毂']
    
    return {
      repairCount: randomInt(1000, 3000),
      totalCost: randomFloat(5000, 15000, 0),
      partTypes: partTypes.map(type => ({
        name: type,
        value: randomInt(100, 500)
      })),
      monthlyRepairs: generateMonthlyData(12, 200, 0.3)
    }
  },

  // 企业排行榜
  getTransportEnterpriseRanking() {
    const companies = [
      '海南港航控股有限公司', '海南航空技术有限公司',
      '海南汽车贸易有限公司', '海南交通投资控股有限公司',
      '海南物流集团有限公司'
    ]

    return companies.map((name, index) => ({
      rank: index + 1,
      name,
      value: randomFloat(2000, 8000, 0),
      change: randomFloat(-10, 20, 1),
      category: '减免税费'
    }))
  }
}

/**
 * 综合统计数据
 */
export const analyticsData = {
  // 关键指标卡片
  getKeyIndicators() {
    return [
      {
        title: '货物总值',
        value: randomFloat(80000, 120000, 0),
        unit: '万元',
        trend: 'up',
        change: randomFloat(5, 15, 1)
      },
      {
        title: '免征税额',
        value: randomFloat(15000, 25000, 0),
        unit: '万元',
        trend: 'up',
        change: randomFloat(8, 20, 1)
      },
      {
        title: '备案企业',
        value: randomInt(800, 1200),
        unit: '家',
        trend: 'up',
        change: randomFloat(3, 12, 1)
      },
      {
        title: 'ERP联网企业',
        value: randomInt(400, 600),
        unit: '家',
        trend: 'up',
        change: randomFloat(2, 10, 1)
      },
      {
        title: '零关税车辆',
        value: randomInt(800, 1500),
        unit: '辆',
        trend: 'up',
        change: randomFloat(10, 25, 1)
      },
      {
        title: '产值申报',
        value: randomInt(200, 400),
        unit: '单',
        trend: 'stable',
        change: randomFloat(-5, 5, 1)
      }
    ]
  },

  // 地区分布数据
  getRegionDistribution() {
    return REGIONS.map(region => ({
      name: region,
      value: randomInt(50, 300),
      percentage: randomFloat(5, 20, 1)
    }))
  },

  // 时间趋势数据
  getTimeTrend(type = 'monthly', months = 12) {
    return generateMonthlyData(months, 1000, 0.3)
  }
}

/**
 * 获取所有模拟数据
 */
export function getAllMockData() {
  return {
    processing: {
      goodsStats: processingBusinessData.getGoodsStats(),
      taxExemption: processingBusinessData.getTaxExemptionData(),
      enterprises: processingBusinessData.getRegisteredEnterprises(),
      ranking: processingBusinessData.getEnterpriseRanking(),
      erpEnterprises: processingBusinessData.getERPEnterprises(),
      topProducts: processingBusinessData.getTopProducts()
    },
    transport: {
      zeroTariffCars: transportBusinessData.getZeroTariffCars(),
      dutyFreeVehicles: transportBusinessData.getDutyFreeVehicles(),
      spareParts: transportBusinessData.getSparePartsRepair(),
      ranking: transportBusinessData.getTransportEnterpriseRanking()
    },
    analytics: {
      indicators: analyticsData.getKeyIndicators(),
      regions: analyticsData.getRegionDistribution(),
      trends: analyticsData.getTimeTrend()
    }
  }
}
