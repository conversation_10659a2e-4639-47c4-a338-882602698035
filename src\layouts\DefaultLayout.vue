<template>
  <div class="flex flex-col h-full bg-gray-100 dark:bg-gray-900">
    <!-- 顶部导航栏 -->
    <nav class="flex items-center justify-between px-6 h-16 bg-white dark:bg-gray-800 shadow-md">
      <div class="flex items-center gap-3">
        <img src="@/assets/logo.svg" alt="Logo" class="w-8 h-8" />
        <span class="text-lg font-semibold text-blue-500 dark:text-blue-400">
          港口领导驾驶舱
        </span>
      </div>

      <div class="flex gap-6">
        <router-link
          v-for="item in menuItems"
          :key="item.path"
          :to="{ path: item.path, query: { device: currentDevice } }"
          class="px-4 py-2 rounded-md transition-colors"
          :class="[
            isActive(item.path)
              ? 'text-blue-500 dark:text-blue-400 bg-blue-50 dark:bg-blue-900'
              : 'text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          ]"
        >
          {{ item.name }}
        </router-link>
      </div>

      <div class="flex items-center gap-4">
        <select
          v-model="currentDevice"
          class="px-3 py-1.5 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
          @change="handleDeviceChange"
        >
          <option value="pc">PC端</option>
          <option value="mobile">移动端</option>
          <option value="wall">大屏端</option>
        </select>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="flex-1 p-6 overflow-auto">
      <slot></slot>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 获取当前路由和路由器
const route = useRoute()
const router = useRouter()

// 导航菜单项
const menuItems = [
  { path: '/processing', name: '加工增值' },
  { path: '/transport', name: '运输物流' }
]

// 当前设备类型
const currentDevice = ref(route.query.device || 'pc')

// 判断导航项是否激活
const isActive = (path) => {
  return route.path.startsWith(path)
}

// 处理设备类型变更
const handleDeviceChange = () => {
  router.push({
    path: route.path,
    query: { device: currentDevice.value }
  })
}
</script>