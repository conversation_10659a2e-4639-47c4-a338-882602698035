<template>
  <div>
    <div class="cards">
      <div v-for="(v, k) in data.stats" :key="k" class="card">{{ k }}: {{ v }}</div>
    </div>
    <div class="trend">
      <div>月度趋势（区内）: {{ data.valueTrend?.区内?.map(i=>i.value).join(', ') }}</div>
      <div>月度趋势（区外）: {{ data.valueTrend?.区外?.map(i=>i.value).join(', ') }}</div>
    </div>
    <div class="pie">企业类型分布: {{ data.companyType?.map(i=>i.name+':'+i.value).join(', ') }}</div>
    <div class="top5">TOP5企业: {{ data.top5Company?.map(i=>i.name+':'+i.value).join(', ') }}</div>
    <div class="erp">ERP联网企业数: {{ data.erpCompanyCount }}</div>
    <div class="erp-top5">ERP联网TOP5关区: {{ data.erpTop5Customs?.map(i=>i.name+':'+i.value).join(', ') }}</div>
    <div class="city-table">主要市县: {{ data.cityTable?.map(i=>i.city+':'+i.value).join(', ') }}</div>
    <div class="area-table">主要区域: {{ data.areaTable?.map(i=>i.area+':'+i.value).join(', ') }}</div>
    <div class="color-blocks">色块分布: {{ data.colorBlocks?.map(i=>i.name+':'+i.value).join(', ') }}</div>
    <div class="tree">树结构: {{ data.tree?.name }}
      <ul>
        <li v-for="c in data.tree?.children || []" :key="c.name">{{ c.name }}: {{ c.value }}</li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ProcessingChart',
  props: { data: Object }
}
</script>
<style scoped>
.card { display:inline-block; margin:4px; padding:4px; border:1px solid #eee; }
</style> 