<template>
  <ChartCard title="交通工具分析" :options="chartOptions" />
</template>

<script setup>
import { computed } from 'vue';
import ChartCard from '../../../components/ChartCard.vue';
import { useThemeStore } from '../../../stores/theme.js';

// 接收父组件传递的数据
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 获取主题状态
const themeStore = useThemeStore();

// 计算图表配置
const chartOptions = computed(() => {
  // 确保数据存在
  if (!props.data || !props.data.vehicleTypes || props.data.vehicleTypes.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    };
  }

  // 提取车辆类型数据
  const vehicleTypes = props.data.vehicleTypes || [];
  
  // 图表配置
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 'center',
      selectedMode: false
    },
    series: [
      {
        name: '车辆类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: themeStore.isDark ? '#0c1c2c' : '#ffffff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: vehicleTypes.map(item => ({
          name: item.name,
          value: item.count,
          itemStyle: {
            color: item.color || ''
          }
        }))
      }
    ]
  };
});
</script>