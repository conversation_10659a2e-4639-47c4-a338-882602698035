// 加工增值模拟数据
export const processingStats = {
  totalValue: {
    value: 1234.56,
    unit: '亿元',
    trend: '+12.3%'
  },
  taxExemption: {
    value: 45.67,
    unit: '亿元',
    trend: '+8.9%'
  },
  registeredEnterprises: {
    value: 789,
    unit: '家',
    trend: '+15.6%'
  },
  declarations: {
    value: 5678,
    unit: '份',
    trend: '+10.2%'
  }
}

export const monthlyTrends = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '货物总值',
    data: [120, 150, 180, 210, 240, 270],
    borderColor: '#36a2eb',
    tension: 0.4
  }]
}

export const enterpriseTypes = {
  labels: ['制造业', '加工业', '服务业', '贸易业', '其他'],
  datasets: [{
    data: [35, 25, 20, 15, 5],
    backgroundColor: [
      '#ff6384',
      '#36a2eb',
      '#ffce56',
      '#4bc0c0',
      '#9966ff'
    ]
  }]
}

export const regionDistribution = {
  labels: ['海口', '三亚', '儋州', '琼海', '文昌'],
  datasets: [{
    label: '企业数量',
    data: [150, 120, 90, 60, 30],
    backgroundColor: '#36a2eb'
  }]
}

export const erpEnterprises = {
  inZone: {
    value: 456,
    trend: '+5.6%'
  },
  outZone: {
    value: 234,
    trend: '+3.2%'
  }
}

export const top5Products = {
  labels: ['电子产品', '机械设备', '医疗器械', '汽车配件', '化工产品'],
  datasets: [{
    label: '加工产值',
    data: [50, 40, 30, 20, 10],
    backgroundColor: '#ff6384'
  }]
}

export const top5Enterprises = {
  labels: ['企业A', '企业B', '企业C', '企业D', '企业E'],
  datasets: [{
    label: '产值（亿元）',
    data: [100, 80, 60, 40, 20],
    backgroundColor: '#4bc0c0'
  }]
}