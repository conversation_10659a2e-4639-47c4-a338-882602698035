<template>
  <div class="production-stats" :class="theme">
    <!-- 数据卡片 -->
    <div class="stats-cards">
      <el-card v-for="stat in statsData" :key="stat.key" class="stat-card">
        <template #header>
          <div class="card-header">
            <span>{{ stat.title }}</span>
            <el-tag :type="stat.tagType" effect="dark">{{ stat.trend }}</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="value">{{ stat.value }} {{ stat.unit }}</div>
          <div class="chart">
            <el-skeleton v-if="loading" :rows="3" animated />
            <el-chart
              v-else
              :option="{
                grid: { top: 10, right: 10, bottom: 10, left: 30 },
                xAxis: {
                  type: 'category',
                  data: monthData,
                  axisLine: { show: false },
                  axisTick: { show: false }
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                series: [{
                  type: 'line',
                  data: stat.chartData,
                  smooth: true,
                  showSymbol: false,
                  lineStyle: { width: 3 },
                  areaStyle: {
                    opacity: 0.1
                  }
                }]
              }"
              autoresize
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>主要产品类型</span>
            <el-tag type="success" effect="dark">月度统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="productTypesChartOption"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>月度产值趋势</span>
            <el-tag type="primary" effect="dark">近6月统计</el-tag>
          </div>
        </template>
        <div class="chart-content">
          <el-skeleton v-if="loading" :rows="8" animated />
          <el-chart
            v-else
            :option="monthlyTrendChartOption"
            autoresize
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores'
import { formatNumber } from '@/utils/format'

// 属性定义
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({
      monthlyValue: 0,
      yearOverYear: 0,
      monthOverMonth: 0,
      productTypes: [],
      monthlyTrend: []
    })
  }
})

// 主题状态
const themeStore = useThemeStore()
const theme = computed(() => themeStore.theme)

// 月份数据
const monthData = ['1月', '2月', '3月', '4月', '5月', '6月']

// 统计卡片数据
const statsData = computed(() => [
  {
    key: 'monthly_value',
    title: '本月产值',
    value: formatNumber(props.data.monthlyValue),
    unit: '万元',
    trend: `同比增长 ${props.data.yearOverYear}%`,
    tagType: props.data.yearOverYear > 0 ? 'success' : 'danger',
    chartData: props.data.monthlyTrend
  },
  {
    key: 'year_on_year',
    title: '同比增长',
    value: props.data.yearOverYear,
    unit: '%',
    trend: props.data.yearOverYear > 0 ? '增速上升' : '增速下降',
    tagType: props.data.yearOverYear > 0 ? 'success' : 'danger',
    chartData: props.data.monthlyTrend.map((v, i, arr) => 
      i > 0 ? ((v - arr[i-1]) / arr[i-1] * 100).toFixed(1) : 0
    )
  },
  {
    key: 'month_on_month',
    title: '环比增长',
    value: props.data.monthOverMonth,
    unit: '%',
    trend: props.data.monthOverMonth > 0 ? '增速上升' : '增速平稳',
    tagType: props.data.monthOverMonth > 0 ? 'success' : 'info',
    chartData: props.data.monthlyTrend.map((v, i, arr) => 
      i > 0 ? ((v - arr[i-1]) / arr[i-1] * 100).toFixed(1) : 0
    )
  }
])

// 产品类型图表配置
const productTypesChartOption = computed(() => ({
  tooltip: { 
    trigger: 'item',
    formatter: '{b}: {c} 万元 ({d}%)'
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'center',
    formatter: name => `${name}: ${props.data.productTypes.find(item => item.name === name)?.value || 0} 万元`
  },
  series: [{
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['40%', '50%'],
    data: props.data.productTypes,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    label: {
      show: true,
      formatter: '{b}: {d}%'
    }
  }]
}))

// 月度趋势图表配置
const monthlyTrendChartOption = computed(() => ({
  tooltip: { 
    trigger: 'axis',
    formatter: params => {
      const [param] = params
      return `${param.name}<br/>${param.seriesName}: ${formatNumber(param.value)} 万元`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: monthData,
    axisLabel: {
      interval: 0
    }
  },
  yAxis: {
    type: 'value',
    name: '万元',
    splitLine: {
      lineStyle: {
        type: 'dashed'
      }
    },
    axisLabel: {
      formatter: value => formatNumber(value)
    }
  },
  series: [{
    name: '产值',
    type: 'bar',
    data: props.data.monthlyTrend,
    barWidth: '40%',
    itemStyle: {
      borderRadius: [8, 8, 0, 0]
    },
    label: {
      show: true,
      position: 'top',
      formatter: value => formatNumber(value)
    }
  }]
}))
</script>

<style scoped>
.production-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--content-gap);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.stat-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
}

.chart {
  height: 100px;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--grid-gap);
  flex: 1;
}

.chart-card {
  background-color: var(--component-bg);
  border: 1px solid var(--border-color);
}

.chart-content {
  height: 300px;
}
</style>