<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海南海关领导视窗</title>
    <script src="https://unpkg.com/vue@3.5.0/dist/vue.global.js"></script>
    <script src="https://unpkg.com/echarts@5.5.0/dist/echarts.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #0a1428;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        #app { width: 100vw; height: 100vh; display: flex; flex-direction: column; }
        
        /* 标题栏 */
        .header {
            background: rgba(15, 23, 42, 0.8);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #1890ff;
        }
        .header h1 {
            color: #1890ff;
            font-size: 28px;
            margin: 0;
        }
        
        /* 切换按钮 */
        .tabs {
            background: rgba(15, 23, 42, 0.6);
            padding: 15px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .tab {
            padding: 10px 20px;
            background: rgba(24, 144, 255, 0.1);
            border: 1px solid #1890ff;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tab:hover { background: rgba(24, 144, 255, 0.2); }
        .tab.active { background: #1890ff; color: white; }
        
        /* 主要内容 */
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* 数据卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(24, 144, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .stat-card h3 {
            color: #1890ff;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .stat-card .value {
            color: #ffffff;
            font-size: 24px;
            font-weight: bold;
        }
        .stat-card .unit {
            color: #8c8c8c;
            font-size: 12px;
        }
        
        /* 图表网格 */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .chart-card {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(24, 144, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
        }
        .chart-card h3 {
            color: #1890ff;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }
        .chart {
            width: 100%;
            height: 300px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 { font-size: 20px; }
            .tabs { flex-direction: column; align-items: center; }
            .stats-grid { grid-template-columns: repeat(2, 1fr); }
            .charts-grid { grid-template-columns: 1fr; }
            .chart { height: 250px; }
        }
        
        @media (min-width: 1920px) {
            .header h1 { font-size: 36px; }
            .stats-grid { grid-template-columns: repeat(6, 1fr); }
            .charts-grid { grid-template-columns: repeat(3, 1fr); }
            .chart { height: 400px; }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>海南海关领导视窗</h1>
        </div>
        
        <div class="tabs">
            <div class="tab" :class="{active: currentView === 'processing'}" @click="currentView = 'processing'">
                加工增值
            </div>
            <div class="tab" :class="{active: currentView === 'transport'}" @click="currentView = 'transport'">
                交通运输工具
            </div>
        </div>
        
        <div class="content">
            <!-- 加工增值视图 -->
            <div v-if="currentView === 'processing'">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>ERP联网企业数量</h3>
                        <div class="value">{{ processingData.erpCount }}</div>
                        <div class="unit">家</div>
                    </div>
                    <div class="stat-card">
                        <h3>加工增值总额</h3>
                        <div class="value">{{ processingData.totalValue }}</div>
                        <div class="unit">亿元</div>
                    </div>
                    <div class="stat-card">
                        <h3>免征税款</h3>
                        <div class="value">{{ processingData.taxExemption }}</div>
                        <div class="unit">万元</div>
                    </div>
                    <div class="stat-card">
                        <h3>备案企业数</h3>
                        <div class="value">{{ processingData.registeredCount }}</div>
                        <div class="unit">家</div>
                    </div>
                </div>
                
                <div class="charts-grid">
                    <div class="chart-card">
                        <h3>ERP联网企业数量TOP5关区</h3>
                        <div id="erp-chart" class="chart"></div>
                    </div>
                    <div class="chart-card">
                        <h3>加工增值产值TOP5企业</h3>
                        <div id="value-chart" class="chart"></div>
                    </div>
                </div>
            </div>
            
            <!-- 交通运输工具视图 -->
            <div v-if="currentView === 'transport'">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>零关税进口车辆</h3>
                        <div class="value">{{ transportData.zeroTariffVehicles }}</div>
                        <div class="unit">台</div>
                    </div>
                    <div class="stat-card">
                        <h3>减免税费总额</h3>
                        <div class="value">{{ transportData.taxReduction }}</div>
                        <div class="unit">万元</div>
                    </div>
                    <div class="stat-card">
                        <h3>车辆类型数</h3>
                        <div class="value">{{ transportData.vehicleTypes }}</div>
                        <div class="unit">种</div>
                    </div>
                </div>
                
                <div class="charts-grid">
                    <div class="chart-card">
                        <h3>车辆类型分布</h3>
                        <div id="vehicle-chart" class="chart"></div>
                    </div>
                    <div class="chart-card">
                        <h3>减免税费企业排行</h3>
                        <div id="tax-chart" class="chart"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted, watch } = Vue;
        
        createApp({
            setup() {
                const currentView = ref('processing');
                
                // 模拟数据
                const processingData = ref({
                    erpCount: 25,
                    totalValue: 142.33,
                    taxExemption: 14.21,
                    registeredCount: 89
                });
                
                const transportData = ref({
                    zeroTariffVehicles: 1420,
                    taxReduction: 3456,
                    vehicleTypes: 8
                });
                
                const initCharts = () => {
                    if (currentView.value === 'processing') {
                        // ERP企业数量图表
                        const erpChart = echarts.init(document.getElementById('erp-chart'));
                        erpChart.setOption({
                            backgroundColor: 'transparent',
                            grid: { top: 20, right: 20, bottom: 40, left: 40 },
                            xAxis: {
                                type: 'category',
                                data: ['海口海关', '洋浦海关', '三亚海关', '马村海关', '文昌海关'],
                                axisLabel: { color: '#ffffff', fontSize: 10 },
                                axisLine: { lineStyle: { color: '#1890ff' } }
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: { color: '#ffffff' },
                                axisLine: { lineStyle: { color: '#1890ff' } },
                                splitLine: { lineStyle: { color: 'rgba(24, 144, 255, 0.2)' } }
                            },
                            series: [{
                                data: [8, 6, 4, 4, 3],
                                type: 'bar',
                                itemStyle: { color: '#1890ff' }
                            }]
                        });
                        
                        // 加工增值企业图表
                        const valueChart = echarts.init(document.getElementById('value-chart'));
                        valueChart.setOption({
                            backgroundColor: 'transparent',
                            grid: { top: 20, right: 20, bottom: 60, left: 40 },
                            xAxis: {
                                type: 'category',
                                data: ['海南钢铁', '洋浦石化', '三亚制造', '海口科技', '文昌航天'],
                                axisLabel: { color: '#ffffff', fontSize: 10, rotate: 30 },
                                axisLine: { lineStyle: { color: '#1890ff' } }
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: { color: '#ffffff' },
                                axisLine: { lineStyle: { color: '#1890ff' } },
                                splitLine: { lineStyle: { color: 'rgba(24, 144, 255, 0.2)' } }
                            },
                            series: [{
                                data: [25.6, 22.1, 18.9, 15.2, 12.8],
                                type: 'bar',
                                itemStyle: { color: '#52c41a' }
                            }]
                        });
                    } else {
                        // 车辆类型分布
                        const vehicleChart = echarts.init(document.getElementById('vehicle-chart'));
                        vehicleChart.setOption({
                            backgroundColor: 'transparent',
                            series: [{
                                name: '车辆类型',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                data: [
                                    { value: 45, name: '小汽车' },
                                    { value: 25, name: '货车' },
                                    { value: 15, name: '客车' },
                                    { value: 10, name: '特种车' },
                                    { value: 5, name: '其他' }
                                ],
                                label: { color: '#ffffff' },
                                itemStyle: {
                                    color: function(params) {
                                        const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
                                        return colors[params.dataIndex];
                                    }
                                }
                            }]
                        });
                        
                        // 减免税费企业排行
                        const taxChart = echarts.init(document.getElementById('tax-chart'));
                        taxChart.setOption({
                            backgroundColor: 'transparent',
                            grid: { top: 20, right: 20, bottom: 60, left: 40 },
                            xAxis: {
                                type: 'category',
                                data: ['海南汽贸', '洋浦物流', '三亚运输', '海口货运', '文昌配送'],
                                axisLabel: { color: '#ffffff', fontSize: 10, rotate: 30 },
                                axisLine: { lineStyle: { color: '#1890ff' } }
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: { color: '#ffffff' },
                                axisLine: { lineStyle: { color: '#1890ff' } },
                                splitLine: { lineStyle: { color: 'rgba(24, 144, 255, 0.2)' } }
                            },
                            series: [{
                                data: [450, 380, 320, 280, 240],
                                type: 'bar',
                                itemStyle: { color: '#faad14' }
                            }]
                        });
                    }
                };
                
                onMounted(() => {
                    setTimeout(initCharts, 100);
                });
                
                watch(currentView, () => {
                    setTimeout(initCharts, 100);
                });
                
                return {
                    currentView,
                    processingData,
                    transportData
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
