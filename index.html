<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>海南海关领导视窗系统</title>
  <link rel="stylesheet" href="/src/styles/index.css">
</head>
<body class="bg-gray-900 text-white">
  <!-- 应用根节点 -->
  <div id="app"></div>

  <!-- 初始加载动画 -->
  <div id="loading" class="fixed inset-0 flex items-center justify-center bg-gray-900 z-50">
    <div class="w-10 h-10 border-3 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
  </div>

  <!-- 主入口脚本 -->
  <script type="module" src="/src/main.js"></script>

  <!-- 移除加载动画 -->
  <script>
    window.addEventListener('load', () => {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.opacity = '0';
        loading.style.transition = 'opacity 0.3s ease';
        setTimeout(() => loading.remove(), 300);
      }
    });
  </script>
</body>
</html>