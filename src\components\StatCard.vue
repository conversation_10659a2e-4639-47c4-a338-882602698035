<template>
  <div class="stat-card">
    <div class="stat-card-header">
      <div class="stat-title">{{ title }}</div>
      <div v-if="trend" class="stat-trend" :class="trendClass">
        <span class="trend-icon">{{ trendIcon }}</span>
        <span class="trend-text">{{ formatChange(change) }}</span>
      </div>
    </div>
    
    <div class="stat-content">
      <div class="stat-value">
        {{ formatValue(value) }}
        <span v-if="unit" class="stat-unit">{{ unit }}</span>
      </div>
      
      <div v-if="description" class="stat-description">
        {{ description }}
      </div>
    </div>
    
    <div v-if="showChart && chartData.length" class="stat-chart">
      <BaseChart
        type="line"
        :data="chartData"
        :config="chartConfig"
        :height="60"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { BaseChart } from './charts/index.js'

// Props定义
const props = defineProps({
  // 标题
  title: {
    type: String,
    required: true
  },
  // 数值
  value: {
    type: [Number, String],
    required: true
  },
  // 单位
  unit: {
    type: String,
    default: ''
  },
  // 趋势方向
  trend: {
    type: String,
    validator: value => ['up', 'down', 'stable'].includes(value)
  },
  // 变化幅度
  change: {
    type: Number,
    default: 0
  },
  // 描述信息
  description: {
    type: String,
    default: ''
  },
  // 是否显示图表
  showChart: {
    type: Boolean,
    default: false
  },
  // 图表数据
  chartData: {
    type: Array,
    default: () => []
  },
  // 精度
  precision: {
    type: Number,
    default: 0
  }
})

// 计算属性
const trendClass = computed(() => ({
  'trend-up': props.trend === 'up',
  'trend-down': props.trend === 'down',
  'trend-stable': props.trend === 'stable'
}))

const trendIcon = computed(() => {
  switch (props.trend) {
    case 'up': return '↗'
    case 'down': return '↘'
    case 'stable': return '→'
    default: return ''
  }
})

const chartConfig = computed(() => ({
  showArea: true,
  smooth: true,
  showLabel: false,
  grid: {
    left: 0,
    right: 0,
    top: 5,
    bottom: 5
  }
}))

// 格式化数值
const formatValue = (value) => {
  if (typeof value === 'string') return value
  
  const num = Number(value)
  if (isNaN(num)) return '--'
  
  // 大数字格式化
  if (num >= 100000000) {
    return (num / 100000000).toFixed(props.precision) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(props.precision) + '万'
  } else {
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: props.precision,
      maximumFractionDigits: props.precision
    })
  }
}

// 格式化变化幅度
const formatChange = (change) => {
  if (!change) return ''
  
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(1)}%`
}
</script>

<style scoped>
.stat-card {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 20px;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stat-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  line-height: 1.4;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
}

.trend-up {
  color: #10b981;
  background: rgba(16, 185, 129, 0.2);
}

.trend-down {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.2);
}

.trend-stable {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.2);
}

.trend-icon {
  font-size: 14px;
}

.stat-content {
  margin-bottom: 16px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stat-unit {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
}

.stat-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

.stat-chart {
  height: 60px;
  margin-top: 12px;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-title {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .stat-card {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-chart {
    height: 40px;
  }
}
</style>
