<template>
  <div class="stat-card">
    <div class="stat-icon">{{ icon }}</div>
    <div class="stat-content">
      <h3>{{ title }}</h3>
      <div class="stat-value">{{ formattedValue }}</div>
      <div class="stat-unit">{{ unit }}</div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'StatCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: '📊'
    }
  },
  setup(props) {
    const formattedValue = computed(() => {
      if (typeof props.value === 'number') {
        return props.value.toLocaleString()
      }
      return props.value || '0'
    })

    return {
      formattedValue
    }
  }
}
</script>

<style scoped>
.stat-card {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 0.8rem;
  color: #ffffff;
  margin-bottom: 5px;
  opacity: 0.8;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 2px;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-unit {
  font-size: 0.7rem;
  color: #ffffff;
  opacity: 0.6;
}
</style>
