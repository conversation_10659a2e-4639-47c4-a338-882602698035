<template>
  <div class="stat-card">
    <div class="stat-icon">{{ icon }}</div>
    <div class="stat-content">
      <h3>{{ title }}</h3>
      <div class="stat-value">{{ formattedValue }}</div>
      <div class="stat-unit">{{ unit }}</div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'StatCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: '📊'
    }
  },
  setup(props) {
    const formattedValue = computed(() => {
      if (typeof props.value === 'number') {
        return props.value.toLocaleString()
      }
      return props.value || '0'
    })

    return {
      formattedValue
    }
  }
}
</script>

<style scoped>
.stat-card {
  background:
    linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(0, 100, 200, 0.08) 100%),
    rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(0, 212, 255, 0.4);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.4s ease;
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 20px rgba(0, 212, 255, 0.1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  background: linear-gradient(135deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  pointer-events: none;
}

.stat-card:hover {
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow:
    0 12px 40px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 30px rgba(0, 212, 255, 0.2);
  transform: translateY(-4px) scale(1.02);
}

.stat-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.6));
  animation: iconGlow 3s ease-in-out infinite alternate;
}

@keyframes iconGlow {
  0% { filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.6)); }
  100% { filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.9)); }
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00d4ff 0%, #ffffff 50%, #00d4ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 4px;
  text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
  font-family: 'Courier New', monospace;
}

.stat-unit {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 0 3px rgba(0, 212, 255, 0.3);
}
</style>
