import { reactive } from 'vue'
import { useProcessingData } from './useProcessingData'
import { useTransportData } from './useTransportData'

export function useDashboardContent() {
  const { processingData, loadProcessingData } = useProcessingData()
  const { transportData, loadTransportData } = useTransportData()

  // 统一的页面内容配置
  const pageConfig = reactive({
    title: '海南海关领导视窗',
    subtitle: 'Hainan Customs Leadership Dashboard',
    
    // 模块配置
    modules: [
      {
        id: 'processing',
        title: '加工增值',
        icon: '🏭',
        description: '加工增值业务统计',
        
        // 统计卡片配置
        stats: [
          {
            id: 'cargoValue',
            title: '货物总值',
            unit: '万元',
            icon: '📦',
            getValue: () => processingData.cargoStats?.totalValue || 0
          },
          {
            id: 'cargoVolume',
            title: '货物总量',
            unit: '吨',
            icon: '⚖️',
            getValue: () => processingData.cargoStats?.totalVolume || 0
          },
          {
            id: 'taxExemption',
            title: '免征税款',
            unit: '万元',
            icon: '💰',
            getValue: () => processingData.taxExemption?.totalAmount || 0
          },
          {
            id: 'registeredCompanies',
            title: '备案企业',
            unit: '家',
            icon: '🏢',
            getValue: () => processingData.registeredCompanies?.total || 0
          },
          {
            id: 'erpInZone',
            title: 'ERP区内企业',
            unit: '家',
            icon: '💻',
            getValue: () => processingData.erpCompanies?.inZone || 0
          },
          {
            id: 'erpOutZone',
            title: 'ERP区外企业',
            unit: '家',
            icon: '🌐',
            getValue: () => processingData.erpCompanies?.outZone || 0
          }
        ],
        
        // 图表配置
        charts: [
          {
            id: 'cargoTrend',
            title: '货物总值及货量月度趋势',
            type: 'line',
            getData: () => processingData.cargoStats?.monthlyTrend
          },
          {
            id: 'taxTrend',
            title: '免征税款月度趋势',
            type: 'bar',
            getData: () => processingData.taxExemption?.monthlyTrend
          },
          {
            id: 'taxRegion',
            title: '免征税款地区分布',
            type: 'horizontal-bar',
            getData: () => processingData.taxExemption?.byRegion
          },
          {
            id: 'companyTypes',
            title: '备案企业类型分布',
            type: 'pie',
            getData: () => processingData.registeredCompanies?.byType
          },
          {
            id: 'erpTrend',
            title: 'ERP联网企业趋势',
            type: 'line',
            getData: () => processingData.erpCompanies?.monthlyTrend
          },
          {
            id: 'keyCompanyTypes',
            title: '重点企业类型分布',
            type: 'pie',
            getData: () => processingData.processingCompanies?.keyCompanyTypes
          },
          {
            id: 'regionDistribution',
            title: '主要地区企业分布',
            type: 'bar',
            getData: () => processingData.processingCompanies?.regionDistribution
          },
          {
            id: 'top5Companies',
            title: '加工增值产值TOP5企业',
            type: 'horizontal-bar',
            getData: () => processingData.processingCompanies?.top5Companies
          }
        ]
      },
      
      {
        id: 'transport',
        title: '交通运输工具',
        icon: '🚗',
        description: '交通运输工具业务统计',
        
        // 统计卡片配置
        stats: [
          {
            id: 'zeroDutyVehicles',
            title: '零关税车辆',
            unit: '辆',
            icon: '🚗',
            getValue: () => transportData.zeroDutyVehicles?.total || 0
          },
          {
            id: 'dutyFreeVehicles',
            title: '免税车辆',
            unit: '辆',
            icon: '🚙',
            getValue: () => transportData.dutyFreeVehicles?.total || 0
          },
          {
            id: 'repairVehicles',
            title: '维修车辆',
            unit: '辆',
            icon: '🔧',
            getValue: () => transportData.mainlandVehicleRepair?.total || 0
          }
        ],
        
        // 图表配置
        charts: [
          {
            id: 'zeroDutyTrend',
            title: '零关税车辆月度趋势',
            type: 'line',
            getData: () => transportData.zeroDutyVehicles?.monthlyTrend
          },
          {
            id: 'zeroDutyTypes',
            title: '零关税车辆类型分布',
            type: 'pie',
            getData: () => transportData.zeroDutyVehicles?.byType
          },
          {
            id: 'dutyFreeTrend',
            title: '免税车辆月度趋势',
            type: 'line',
            getData: () => transportData.dutyFreeVehicles?.monthlyTrend
          },
          {
            id: 'dutyFreeCategories',
            title: '免税车辆类别分布',
            type: 'pie',
            getData: () => transportData.dutyFreeVehicles?.byCategory
          },
          {
            id: 'repairTrend',
            title: '内地车辆维修趋势',
            type: 'bar',
            getData: () => transportData.mainlandVehicleRepair?.monthlyTrend
          },
          {
            id: 'taxReductionRanking',
            title: '交通工具减免税费企业排行',
            type: 'horizontal-bar',
            getData: () => transportData.taxReductionRanking
          },
          {
            id: 'equipmentTaxReduction',
            title: '生产设备减免税费企业排行',
            type: 'horizontal-bar',
            getData: () => transportData.equipmentTaxReduction
          }
        ]
      }
    ]
  })

  // 加载所有数据
  const loadAllData = async () => {
    await Promise.all([
      loadProcessingData(),
      loadTransportData()
    ])
  }

  return {
    pageConfig,
    processingData,
    transportData,
    loadAllData
  }
}
