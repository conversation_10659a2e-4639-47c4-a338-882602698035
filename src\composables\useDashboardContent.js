import { reactive } from 'vue'
import { useProcessingData } from './useProcessingData'
import { useTransportData } from './useTransportData'

export function useDashboardContent() {
  const { processingData, loadProcessingData } = useProcessingData()
  const { transportData, loadTransportData } = useTransportData()

  // 统一的页面内容配置
  const pageConfig = reactive({
    title: '中国（海南）国际贸易单一窗口领导视窗',
    subtitle: 'China (Hainan) International Trade Single Window Leadership Dashboard',

    // 模块配置
    modules: [
      {
        id: 'processing',
        title: '加工增值',
        icon: '🏭',
        description: '加工增值业务统计分析',
        
        // 统计卡片配置 - 按需求表实现
        stats: [
          {
            id: 'cargoValue',
            title: '货物总值',
            unit: '万元',
            icon: '📦',
            getValue: () => processingData.cargoValueAndVolume?.totalValue || 0
          },
          {
            id: 'cargoVolume',
            title: '货物总量',
            unit: '吨',
            icon: '⚖️',
            getValue: () => processingData.cargoValueAndVolume?.totalVolume || 0
          },
          {
            id: 'taxExemption',
            title: '免征税款',
            unit: '万元',
            icon: '💰',
            getValue: () => processingData.taxExemption?.totalAmount || 0
          },
          {
            id: 'registeredEnterprises',
            title: '备案企业',
            unit: '家',
            icon: '🏢',
            getValue: () => processingData.registeredEnterprises?.total || 0
          },
          {
            id: 'erpInZone',
            title: 'ERP区内企业',
            unit: '家',
            icon: '💻',
            getValue: () => processingData.erpConnectedEnterprises?.inZoneEnterprises?.total || 0
          },
          {
            id: 'erpOutZone',
            title: 'ERP区外企业',
            unit: '家',
            icon: '🌐',
            getValue: () => processingData.erpConnectedEnterprises?.outZoneEnterprises?.total || 0
          },
          {
            id: 'processingDeclarations',
            title: '加工产值申报单',
            unit: '份',
            icon: '📋',
            getValue: () => processingData.processingValueDeclarations?.total || 0
          },
          {
            id: 'shipmentApplications',
            title: '发货申请',
            unit: '份',
            icon: '🚚',
            getValue: () => processingData.shipmentApplications?.total || 0
          }
        ],
        
        // 图表配置 - 按需求表752-817行实现
        charts: [
          {
            id: 'cargoValueVolumeTrend',
            title: '货物总值及货量时间趋势',
            type: 'line',
            getData: () => processingData.cargoValueAndVolume?.timeTrend
          },
          {
            id: 'cargoVolumeStats',
            title: '货物量维度统计',
            type: 'bar',
            getData: () => processingData.cargoValueAndVolume?.volumeStats
          },
          {
            id: 'taxExemptionTrend',
            title: '免征税款时间趋势',
            type: 'line',
            getData: () => processingData.taxExemption?.timeTrend
          },
          {
            id: 'taxExemptionVolume',
            title: '免征税款业务量统计',
            type: 'bar',
            getData: () => processingData.taxExemption?.volumeStats
          },
          {
            id: 'taxExemptionRegion',
            title: '免征税款地区分布',
            type: 'horizontal-bar',
            getData: () => processingData.taxExemption?.regionDistribution
          },
          {
            id: 'registeredEnterprisesTrend',
            title: '备案企业时间趋势',
            type: 'line',
            getData: () => processingData.registeredEnterprises?.timeTrend
          },
          {
            id: 'registeredEnterprisesVolume',
            title: '备案企业类型分布',
            type: 'pie',
            getData: () => processingData.registeredEnterprises?.volumeStats
          },
          {
            id: 'importMaterialRanking',
            title: '进港进口原辅料减免税费企业排行',
            type: 'horizontal-bar',
            getData: () => processingData.importMaterialTaxReduction?.enterpriseRanking
          },
          {
            id: 'processingDeclarationsTrend',
            title: '加工产值申报单时间趋势',
            type: 'line',
            getData: () => processingData.processingValueDeclarations?.timeTrend
          },
          {
            id: 'processingDeclarationsVolume',
            title: '加工产值申报单业务量统计',
            type: 'bar',
            getData: () => processingData.processingValueDeclarations?.volumeStats
          },
          {
            id: 'shipmentApplicationsTrend',
            title: '发货申请时间趋势',
            type: 'line',
            getData: () => processingData.shipmentApplications?.timeTrend
          },
          {
            id: 'shipmentApplicationsVolume',
            title: '发货申请业务量统计',
            type: 'bar',
            getData: () => processingData.shipmentApplications?.volumeStats
          },
          {
            id: 'keyEnterpriseTypes',
            title: '重点企业类型分布排名',
            type: 'pie',
            getData: () => processingData.keyEnterpriseTypes?.distribution
          },
          {
            id: 'regionalDistribution',
            title: '主要地区市县分布排名',
            type: 'bar',
            getData: () => processingData.regionalDistribution?.distribution
          },
          {
            id: 'top5ProcessingEnterprises',
            title: '加工增值产值TOP5企业排名',
            type: 'horizontal-bar',
            getData: () => processingData.top5ProcessingEnterprises?.ranking
          },
          {
            id: 'erpInZoneVolume',
            title: 'ERP区内企业业务量统计',
            type: 'bar',
            getData: () => processingData.erpConnectedEnterprises?.inZoneEnterprises?.volumeStats
          },
          {
            id: 'erpOutZoneVolume',
            title: 'ERP区外企业业务量统计',
            type: 'bar',
            getData: () => processingData.erpConnectedEnterprises?.outZoneEnterprises?.volumeStats
          },
          {
            id: 'mainProcessingProductsRanking',
            title: '主要加工产品TOP5排名',
            type: 'horizontal-bar',
            getData: () => processingData.mainProcessingProducts?.ranking
          },
          {
            id: 'mainProcessingProductsTrend',
            title: '主要加工产品时间趋势',
            type: 'line',
            getData: () => processingData.mainProcessingProducts?.timeTrend
          },
          {
            id: 'materialProductRelationRanking',
            title: '热门料件与成品对应关系排名',
            type: 'horizontal-bar',
            getData: () => processingData.materialProductRelation?.ranking
          },
          {
            id: 'domesticSalesRegionsRanking',
            title: '内销主要区域TOP5排名',
            type: 'horizontal-bar',
            getData: () => processingData.domesticSalesRegions?.ranking
          },
          {
            id: 'domesticSalesTrend',
            title: '内销时间趋势',
            type: 'line',
            getData: () => processingData.domesticSalesRegions?.timeTrend
          },
          {
            id: 'monthlyTaxExemptionTrend',
            title: '免征税额月度趋势',
            type: 'line',
            getData: () => processingData.monthlyTaxExemptionTrend?.timeTrend
          }
        ]
      },
      
      {
        id: 'transport',
        title: '交通运输工具',
        icon: '🚗',
        description: '交通运输工具业务统计',
        
        // 统计卡片配置 - 按需求表869-918行实现
        stats: [
          {
            id: 'zeroDutyVehicles',
            title: '零关税进口小汽车',
            unit: '辆',
            icon: '🚗',
            getValue: () => transportData.zeroDutyVehicles?.total || 0
          },
          {
            id: 'dutyFreeVehiclesAfterClosure',
            title: '封关后免税交通运输工具',
            unit: '辆',
            icon: '🚙',
            getValue: () => transportData.dutyFreeVehiclesAfterClosure?.total || 0
          },
          {
            id: 'mainlandVehicleRepair',
            title: '内地运输工具维修',
            unit: '辆',
            icon: '🔧',
            getValue: () => transportData.mainlandVehicleRepair?.total || 0
          },
          {
            id: 'vehicleYachtTaxReduction',
            title: '交通工具及游艇减免税费企业',
            unit: '家',
            icon: '⛵',
            getValue: () => transportData.vehicleYachtTaxReductionRanking?.fileManagement?.totalEnterprises || 0
          },
          {
            id: 'productionEquipmentTaxReduction',
            title: '自用生产设备减免税费企业',
            unit: '家',
            icon: '🏭',
            getValue: () => transportData.productionEquipmentTaxReductionRanking?.fileManagement?.totalEnterprises || 0
          }
        ],
        
        // 图表配置 - 按需求表869-918行实现
        charts: [
          {
            id: 'zeroDutyVehiclesTrend',
            title: '零关税进口小汽车时间趋势',
            type: 'line',
            getData: () => transportData.zeroDutyVehicles?.timeTrend
          },
          {
            id: 'zeroDutyVehiclesVolume',
            title: '零关税进口小汽车业务量统计',
            type: 'bar',
            getData: () => transportData.zeroDutyVehicles?.volumeStats
          },
          {
            id: 'zeroDutyVehiclesRanking',
            title: '零关税进口小汽车排名统计',
            type: 'horizontal-bar',
            getData: () => transportData.zeroDutyVehicles?.ranking
          },
          {
            id: 'dutyFreeVehiclesAfterClosureTrend',
            title: '封关后免税交通运输工具时间趋势',
            type: 'line',
            getData: () => transportData.dutyFreeVehiclesAfterClosure?.timeTrend
          },
          {
            id: 'dutyFreeVehiclesAfterClosureVolume',
            title: '封关后免税交通运输工具业务量统计',
            type: 'bar',
            getData: () => transportData.dutyFreeVehiclesAfterClosure?.volumeStats
          },
          {
            id: 'dutyFreeVehiclesAfterClosureRanking',
            title: '封关后免税交通运输工具排名统计',
            type: 'horizontal-bar',
            getData: () => transportData.dutyFreeVehiclesAfterClosure?.ranking
          },
          {
            id: 'mainlandVehicleRepairTrend',
            title: '内地运输工具维修时间趋势',
            type: 'line',
            getData: () => transportData.mainlandVehicleRepair?.timeTrend
          },
          {
            id: 'mainlandVehicleRepairVolume',
            title: '内地运输工具维修业务量统计',
            type: 'bar',
            getData: () => transportData.mainlandVehicleRepair?.volumeStats
          },
          {
            id: 'mainlandVehicleRepairRanking',
            title: '内地运输工具维修排名统计',
            type: 'horizontal-bar',
            getData: () => transportData.mainlandVehicleRepair?.ranking
          },
          {
            id: 'zeroDutyBusinessAnalysis',
            title: '零关税业务综合分析',
            type: 'bar',
            getData: () => transportData.zeroDutyBusinessAnalysis?.comprehensiveAnalysis
          },
          {
            id: 'productionEquipmentAnalysis',
            title: '自用生产设备负面清单综合分析',
            type: 'bar',
            getData: () => transportData.productionEquipmentAnalysis?.comprehensiveAnalysis
          },
          {
            id: 'vehicleYachtTaxReductionRanking',
            title: '交通工具及游艇减免税费企业排行',
            type: 'horizontal-bar',
            getData: () => transportData.vehicleYachtTaxReductionRanking?.enterpriseRanking
          },
          {
            id: 'productionEquipmentTaxReductionRanking',
            title: '自用生产设备减免税费企业排行',
            type: 'horizontal-bar',
            getData: () => transportData.productionEquipmentTaxReductionRanking?.enterpriseRanking
          }
        ]
      }
    ]
  })

  // 加载所有数据
  const loadAllData = async () => {
    await Promise.all([
      loadProcessingData(),
      loadTransportData()
    ])
  }

  return {
    pageConfig,
    processingData,
    transportData,
    loadAllData
  }
}
