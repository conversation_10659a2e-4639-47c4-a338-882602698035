import { BaseDataAdapter } from './baseAdapter'

export class ProcessingDataAdapter extends BaseDataAdapter {
  constructor(rawData) {
    super(rawData)
  }

  // 获取总览数据
  getOverview() {
    const statistics = this._calculateStatistics(this.rawData, 'processingVolume')
    const categoryDistribution = this._groupBy(this.rawData, 'category', 'processingVolume')
    
    return {
      ...statistics,
      categoryDistribution,
      charts: {
        line: this._getLineChartData(),
        bar: this._getBarChartData(),
        pie: this._getPieChartData()
      }
    }
  }

  // 获取趋势数据
  getTrend() {
    const timeSeries = this._processTimeSeries(this.rawData, 'date', 'processingVolume')
    const growthRate = this._calculateGrowthRate(timeSeries)

    return {
      timeSeries,
      growthRate
    }
  }

  // 获取详细数据
  getDetails() {
    return {
      categories: [...new Set(this.rawData.map(item => item.category))],
      records: this.rawData.map(item => ({
        date: item.date,
        category: item.category,
        volume: item.processingVolume,
        value: item.processingValue
      }))
    }
  }

  // 私有方法：折线图数据
  _getLineChartData() {
    return this._processTimeSeries(this.rawData, 'date', 'processingVolume')
  }

  // 私有方法：柱状图数据
  _getBarChartData() {
    return this._groupBy(this.rawData, 'category', 'processingVolume')
  }

  // 私有方法：饼图数据
  _getPieChartData() {
    return this._groupBy(this.rawData, 'category', 'processingVolume')
  }

  // 计算增长率
  _calculateGrowthRate(timeSeries) {
    if (timeSeries.length < 2) return 0

    const first = timeSeries[0].value
    const last = timeSeries[timeSeries.length - 1].value

    return ((last - first) / first * 100).toFixed(2)
  }
} 